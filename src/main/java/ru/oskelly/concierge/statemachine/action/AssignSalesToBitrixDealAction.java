package ru.oskelly.concierge.statemachine.action;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.statemachine.StateContext;
import org.springframework.statemachine.action.Action;
import org.springframework.stereotype.Component;
import ru.oskelly.concierge.data.model.PurchaseOrder;
import ru.oskelly.concierge.data.model.enums.PurchaseOrderStatusEnum;
import ru.oskelly.concierge.data.repository.PurchaseOrderRepository;
import ru.oskelly.concierge.exception.BitrixDealException;
import ru.oskelly.concierge.service.BitrixService;
import ru.oskelly.concierge.statemachine.events.PurchaseOrderTransitionEvent;

/**
 * Назначение Сейлза ответственным на сделку в Bitrix
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AssignSalesToBitrixDealAction implements Action<PurchaseOrderStatusEnum, PurchaseOrderTransitionEvent> {

    private final BitrixService bitrixService;
    private final PurchaseOrderRepository purchaseOrderRepository;

    @Override
    public void execute(StateContext<PurchaseOrderStatusEnum, PurchaseOrderTransitionEvent> stateContext) {
        final Long orderId = (Long) stateContext.getExtendedState()
            .getVariables()
            .get("orderId");

        if (orderId == null) {
            return;
        }

        PurchaseOrder purchaseOrder = purchaseOrderRepository.findById(orderId)
            .orElse(null);

        if (purchaseOrder == null) {
            return;
        }

        Long bitrixSalesId = purchaseOrder.getBitrixSalesId();
        Long bitrixDealId = purchaseOrder.getBitrixDealId();

        if (bitrixSalesId == null) {
            log.warn("Не найден ID Bitrix для Сейлза в заявке {}", orderId);
            return;
        }

        try {
            bitrixService.assignUserToDeal(bitrixSalesId, bitrixDealId);
        } catch (Exception exception) {
            throw new BitrixDealException(
                String.format("Не удалось назначить пользователя на сделку Bitrix. OrderId = %s, BitrixUserId = %s, BitrixDealId = %s",
                    orderId,
                    bitrixSalesId,
                    bitrixDealId),
                exception,
                orderId,
                500);
        }
    }
}
