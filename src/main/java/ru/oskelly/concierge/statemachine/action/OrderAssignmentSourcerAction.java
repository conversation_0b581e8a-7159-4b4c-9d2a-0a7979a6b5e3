package ru.oskelly.concierge.statemachine.action;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.statemachine.StateContext;
import org.springframework.statemachine.action.Action;
import org.springframework.stereotype.Component;
import ru.oskelly.concierge.data.model.enums.PurchaseOrderStatusEnum;
import ru.oskelly.concierge.data.repository.PurchaseOrderRepository;
import ru.oskelly.concierge.event.OrderAssignmentSourcerStartEvent;
import ru.oskelly.concierge.statemachine.events.PurchaseOrderTransitionEvent;

/**
 * Запуск процесса назначения заявки на Сорсера
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class OrderAssignmentSourcerAction implements Action<PurchaseOrderStatusEnum, PurchaseOrderTransitionEvent> {

    private final ApplicationEventPublisher applicationEventPublisher;
    private final PurchaseOrderRepository purchaseOrderRepository;

    /**
     * Запрос на получение свободного Сорсера для заявки
     */
    @Override
    public void execute(StateContext<PurchaseOrderStatusEnum, PurchaseOrderTransitionEvent> stateContext) {
        Long orderId = (Long) stateContext
            .getExtendedState()
            .getVariables()
            .get("orderId");

        if (orderId == null) {
            log.warn("Попытка запуска назначения Сорсера с null orderId, пропуск действия");
            return;
        }

        boolean existsSourcer = purchaseOrderRepository.existsByIdAndSourcerIdNotNull(orderId);

        if (existsSourcer) {
            log.warn("Попытка запуска назначения Сорсера для заявки {}, но она уже имеет назначенного Сорсера, пропуск действия", orderId);
            return;
        }

        OrderAssignmentSourcerStartEvent startEvent = new OrderAssignmentSourcerStartEvent(orderId);
        applicationEventPublisher.publishEvent(startEvent);
    }
}
