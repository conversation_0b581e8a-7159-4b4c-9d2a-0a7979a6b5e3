package ru.oskelly.concierge.statemachine.action;

import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.statemachine.StateContext;
import org.springframework.statemachine.action.Action;
import org.springframework.stereotype.Component;
import ru.oskelly.concierge.common.ContextConstants;
import ru.oskelly.concierge.common.ThreadLocalContext;
import ru.oskelly.concierge.data.model.enums.PurchaseOrderStatusEnum;
import ru.oskelly.concierge.statemachine.events.PurchaseOrderTransitionEvent;

/**
 * Обработка ошибок, возникающих в действиях при переходах по статусам
 */
@Slf4j
@Component
public class ErrorHandleAction implements Action<PurchaseOrderStatusEnum, PurchaseOrderTransitionEvent> {

    @Override
    public void execute(StateContext<PurchaseOrderStatusEnum, PurchaseOrderTransitionEvent> stateContext) {
        val stateMachineId = ThreadLocalContext.get(ContextConstants.STATE_MACHINE_ID, Long.class);
        String errorMessage = stateContext.getException().getMessage();
        String sourceStatus = stateContext.getSource().getId().name();
        String targetStatus = stateContext.getTarget().getId().name();

        String message = String.format(
            "Возникла ошибка при переходе заявки из %s в %s: %s. StateMachineId: %s",
            sourceStatus,
            targetStatus,
            errorMessage,
            stateMachineId);

        log.error(message);
    }
}
