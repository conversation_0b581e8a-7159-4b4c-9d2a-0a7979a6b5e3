package ru.oskelly.concierge.statemachine.action;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.statemachine.StateContext;
import org.springframework.statemachine.action.Action;
import org.springframework.stereotype.Component;
import ru.oskelly.concierge.data.model.enums.PurchaseOrderStatusEnum;
import ru.oskelly.concierge.data.repository.PurchaseOrderRepository;
import ru.oskelly.concierge.event.OrderAssignmentSalesStartEvent;
import ru.oskelly.concierge.statemachine.events.PurchaseOrderTransitionEvent;

/**
 * Запуск процесса назначения заявки на Сейлза
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class OrderAssignmentSalesAction implements Action<PurchaseOrderStatusEnum, PurchaseOrderTransitionEvent> {

    private final ApplicationEventPublisher applicationEventPublisher;
    private final PurchaseOrderRepository purchaseOrderRepository;

    /**
     * Запрос на получение свободного Сейлза для заявки
     */
    @Override
    public void execute(StateContext<PurchaseOrderStatusEnum, PurchaseOrderTransitionEvent> stateContext) {
        Long orderId = (Long) stateContext
            .getExtendedState()
            .getVariables()
            .get("orderId");

        log.info("Выполнение действия OrderAssignmentSalesAction для заявки с ID: {}", orderId);
        if (orderId == null) {
            log.warn("Не удалось получить ID заявки из контекста состояния. Прерывание действия OrderAssignmentSalesAction.");
            return;
        }

        boolean existsSales = purchaseOrderRepository.existsByIdAndSalesIdNotNull(orderId);

        if (existsSales) {
            log.warn("Заявка с ID: {} уже имеет назначенного Сейлза. Прерывание действия OrderAssignmentSalesAction.", orderId);
            return;
        }

        OrderAssignmentSalesStartEvent startEvent = new OrderAssignmentSalesStartEvent(orderId);
        applicationEventPublisher.publishEvent(startEvent);
    }
}
