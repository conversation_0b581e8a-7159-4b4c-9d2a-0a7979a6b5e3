package ru.oskelly.concierge.statemachine.listener;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.messaging.Message;
import org.springframework.statemachine.listener.StateMachineListenerAdapter;
import org.springframework.statemachine.state.State;
import org.springframework.stereotype.Component;
import ru.oskelly.concierge.common.ContextConstants;
import ru.oskelly.concierge.common.ThreadLocalContext;
import ru.oskelly.concierge.data.model.PurchaseOrderStateHistory;
import ru.oskelly.concierge.data.model.enums.PurchaseOrderStatusEnum;
import ru.oskelly.concierge.data.repository.PurchaseOrderStateHistoryRepository;
import ru.oskelly.concierge.event.PurchaseOrderStatusChangedEvent;
import ru.oskelly.concierge.statemachine.events.PurchaseOrderTransitionEvent;

import java.time.ZonedDateTime;

/**
 * Кастомный слушатель состояний, для обработки некорректных событий
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class CustomStateMachineListener extends StateMachineListenerAdapter<PurchaseOrderStatusEnum, PurchaseOrderTransitionEvent> {
    private final PurchaseOrderStateHistoryRepository historyRepository;
    private final ApplicationEventPublisher eventPublisher;

    @Override
    public void stateChanged(State<PurchaseOrderStatusEnum, PurchaseOrderTransitionEvent> from, State<PurchaseOrderStatusEnum, PurchaseOrderTransitionEvent> to) {
        var stateMachineId = ThreadLocalContext.get(ContextConstants.STATE_MACHINE_ID, Long.class);

        PurchaseOrderStatusEnum sourceState;
        PurchaseOrderStatusEnum targetState;
        if (from == null) {
            return;
        }

        sourceState = from.getId();
        targetState = to.getId();

        String comment = ThreadLocalContext.get(ContextConstants.COMMENT, String.class);

        log.info("Изменение состояния от  {} до {}", sourceState, targetState);
        PurchaseOrderStateHistory historyEntry = PurchaseOrderStateHistory.builder()
                .purchaseOrderId(stateMachineId)
                .userId(ThreadLocalContext.get(ContextConstants.USER_ID, Long.class))
                .sourceState(sourceState)
                .targetState(targetState)
                .reasonReturn(ThreadLocalContext.get(ContextConstants.REASON_RETURN, String.class))
                .transitionDate(ZonedDateTime.now())
                .comment(comment)
                .build();
        historyRepository.save(historyEntry);
        log.info("Сохранение истории переходов состояний: {} -> {} for order {}",
                historyEntry.getSourceState(), historyEntry.getTargetState(), stateMachineId);

        eventPublisher.publishEvent(new PurchaseOrderStatusChangedEvent(stateMachineId));
    }

    @Override
    public void eventNotAccepted(Message<PurchaseOrderTransitionEvent> event) {
        var stateMachineId = ThreadLocalContext.get(ContextConstants.STATE_MACHINE_ID, Long.class);
        var machineId = stateMachineId != null ? stateMachineId : "-";
        // Установите флаг ошибки в контексте
        ThreadLocalContext.put(ContextConstants.STATE_MACHINE_ERROR, true);
        ThreadLocalContext.put(ContextConstants.STATE_MACHINE_ERROR_MESSAGE,
                String.format("Событие %s недопустимо в текущем состоянии машины (ID: %s)",
                        event.getPayload(), machineId));

        log.error("Событие {} недопустимо для машины с ID {}", event.getPayload(), machineId);
    }
}
