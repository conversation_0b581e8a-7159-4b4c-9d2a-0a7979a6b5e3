package ru.oskelly.concierge;

import io.swagger.v3.oas.annotations.OpenAPIDefinition;
import io.swagger.v3.oas.annotations.info.Info;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import ru.oskelly.concierge.bitrix.client.BitrixClient;
import ru.oskelly.concierge.bitrix.client.BitrixOpenLineClient;

@EntityScan(basePackages = "ru.oskelly.concierge.data.model")
@SpringBootApplication
@EnableFeignClients(clients = {
        BitrixClient.class,
        BitrixOpenLineClient.class
})
@OpenAPIDefinition(
        info = @Info(title = "Concierge API", version = "1.0", description = "API Documentation")
)
@EnableScheduling
@EnableAsync
public class OskellyConciergeApplication {

    public static void main(String[] args) {
        SpringApplication.run(OskellyConciergeApplication.class, args);
    }

}
