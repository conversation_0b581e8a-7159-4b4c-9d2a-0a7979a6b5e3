package ru.oskelly.concierge.data.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import ru.oskelly.concierge.controller.dto.ProductConditionDTO;
import ru.oskelly.concierge.controller.dto.miniapps.ShopperProposedOfferDTO;
import ru.oskelly.concierge.controller.dto.miniapps.OfferDetailsDTO;
import ru.oskelly.concierge.controller.dto.miniapps.OrderInfoDTO;
import ru.oskelly.concierge.controller.dto.miniapps.PriceInfoDTO;
import ru.oskelly.concierge.data.model.Offer;
import ru.oskelly.concierge.data.model.ProposedOffer;
import ru.oskelly.concierge.data.model.enums.AggregateOfferStatus;
import ru.oskelly.concierge.data.model.enums.PurchaseOrderStatusEnum;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Set;

@Mapper(componentModel = "spring")
public interface OfferDetailsMapper {

    @Mapping(target = "id", source = "offer.id")
    @Mapping(target = "aggregateStatus", source = "offer", qualifiedByName = "mapRootStatus")
    @Mapping(target = "status", source = "offer.status")
//    @Mapping(target = "statusLocalized", source = "offer.shipment.purchaseOrder.status", qualifiedByName = "mapStatusLocalized")
//    @Mapping(target = "statusDescriptionLocalized", source = "offer.shipment.purchaseOrder.status", qualifiedByName = "mapStatusDescriptionLocalized")
    @Mapping(target = "expirationDate", source = "offer", qualifiedByName = "mapExpirationDate")
    @Mapping(target = "ordersInfo", source = "offer.shipment.purchaseOrder.orders", qualifiedByName = "mapOrdersInfo")
    @Mapping(target = "proposedOffers", source = "offer.proposedOffers", qualifiedByName = "mapProposedOffers")
    @Mapping(target = "creationDate", source = "offer.creationDate")
    @Mapping(target = "categoryName", source = "offer.shipment.categoryName")
    @Mapping(target = "brandName", source = "offer.shipment.brandName")
    @Mapping(target = "materialAttributeName", source = "offer.shipment.materialAttributeName")
    @Mapping(target = "colorAttributeName", source = "offer.shipment.colorAttributeName")
    @Mapping(target = "modelName", source = "offer.shipment.modelName")
    @Mapping(target = "size", source = "offer.shipment.shipmentSize")
    @Mapping(target = "description", source = "offer.shipment.description")
    @Mapping(target = "images", source = "offer.shipment.images")
    @Mapping(target = "links", source = "offer.shipment.links")
    OfferDetailsDTO toOfferDetailsDTO(Offer offer);

    @Named("mapRootStatus")
    default AggregateOfferStatus mapRootStatus(Offer offer) {
        var status = offer.getStatus();
        return switch (status) {
            case ORDER_PAID -> AggregateOfferStatus.COMPLETED;
            case CANCELLED_BY_EXECUTOR, CANCELLED_NOT_RELEVANT -> AggregateOfferStatus.ARCHIVED;
            case NEW, IN_PROGRESS, PROCESSED, AWAITING_CUSTOMER_DECISION -> AggregateOfferStatus.ACTIVE;
        };
    }

//    @Named("mapStatusLocalized")
//    default String mapStatusLocalized(PurchaseOrderStatusEnum status) {
//        return status != null ? status.getDescription() : null;
//    }
//
//    @Named("mapStatusDescriptionLocalized")
//    default String mapStatusDescriptionLocalized(PurchaseOrderStatusEnum status) {
//        // Здесь можно добавить более детальные описания статусов
//        return switch (status) {
//            case AWAITING_CLIENT_ANSWER -> "Предложение отправлено клиенту и ожидается его решение";
//            case AWAITING_SEND_TO_CLIENT -> "Предложение готово к отправке клиенту";
//            case IN_PROGRESS_SALES -> "Заявка обрабатывается менеджером по продажам";
//            case IN_PROGRESS_SOURCER -> "Заявка обрабатывается специалистом по поиску";
//            case DONE -> "Заявка успешно завершена";
//            case REJECTED -> "Заявка была отклонена";
//            case CANCELLED -> "Заявка была отменена";
//            default -> status != null ? status.getDescription() : null;
//        };
//    }

//    @Named("mapExpirationDate")
//    default ZonedDateTime mapExpirationDate(Offer offer) {
//        // Находим минимальную дату validUntil среди всех proposedOffers
//        return offer.getProposedOffers().stream()
//                .map(ProposedOffer::getValidUntil)
//                .filter(java.util.Objects::nonNull)
//                .min(java.time.ZonedDateTime::compareTo)
//                .orElse(null);
//    }

    @Named("mapOrdersInfo")
    default List<OrderInfoDTO> mapOrdersInfo(List<Long> orders) {
        if (orders == null || orders.isEmpty()) {
            return List.of();
        }
        return orders.stream()
                .map(orderId -> new OrderInfoDTO(orderId, "UNKNOWN"))
                .toList();
    }

    @Named("mapProposedOffers")
    default List<ShopperProposedOfferDTO> mapProposedOffers(Set<ProposedOffer> proposedOffers) {
        if (proposedOffers == null || proposedOffers.isEmpty()) {
            return List.of();
        }
        return proposedOffers.stream()
                .map(this::mapProposedOffer)
                .toList();
    }

//    @Mapping(target = "id", source = "id")
//    @Mapping(target = "statusDescription", source = ".", qualifiedByName = "mapProposedOfferStatusDescription")
//    @Mapping(target = "deliveryDate", source = "deliveryDate")
//    @Mapping(target = "validUntil", source = "validUntil")
//    @Mapping(target = "timezone", constant = "Europe/Moscow") // По умолчанию, можно сделать настраиваемым
//    @Mapping(target = "prices", source = ".", qualifiedByName = "mapPriceInfo")
//    @Mapping(target = "condition", source = "productConditionId", qualifiedByName = "mapProductCondition")
//    @Mapping(target = "hasReceipt", source = "hasReceipt")
//    @Mapping(target = "isCompleteSet", source = "completeSet")
//    @Mapping(target = "comment", source = "comment")
//    ShopperProposedOfferDTO mapProposedOffer(ProposedOffer proposedOffer);

    // ToDO
    @Named("mapProposedOfferStatusDescription")
    default String mapProposedOfferStatusDescription(ProposedOffer proposedOffer) {
        if (Boolean.TRUE.equals(proposedOffer.getIsSentToCustomer())) {
            return "Это предложение было выбрано менеджером для отправки покупателю";
        }
        return null;
    }

    @Named("mapPriceInfo")
    default PriceInfoDTO mapPriceInfo(ProposedOffer proposedOffer) {
        BigDecimal currencyPrice = proposedOffer.getCurrencyPrice();
        BigDecimal rublePrice = proposedOffer.getRublePrice();
        BigDecimal commission = proposedOffer.getCommission();
        
        BigDecimal currencyPriceWithCommission = null;
        BigDecimal localPriceWithCommission = null;
        
        if (currencyPrice != null && commission != null) {
            BigDecimal commissionMultiplier = BigDecimal.ONE.add(commission.divide(BigDecimal.valueOf(100), 4, RoundingMode.HALF_UP));
            currencyPriceWithCommission = currencyPrice.multiply(commissionMultiplier).setScale(2, RoundingMode.HALF_UP);
        }
        
        if (rublePrice != null && commission != null) {
            BigDecimal commissionMultiplier = BigDecimal.ONE.add(commission.divide(BigDecimal.valueOf(100), 4, RoundingMode.HALF_UP));
            localPriceWithCommission = rublePrice.multiply(commissionMultiplier).setScale(2, RoundingMode.HALF_UP);
        }
        
        return new PriceInfoDTO(
                proposedOffer.getCurrency(),
                currencyPrice,
                rublePrice,
                currencyPriceWithCommission,
                localPriceWithCommission
        );
    }

    @Named("mapProductCondition")
    default ProductConditionDTO mapProductCondition(Long productConditionId) {
        if (productConditionId == null) return null;
        return new ProductConditionDTO(productConditionId, null, null);
    }
}
