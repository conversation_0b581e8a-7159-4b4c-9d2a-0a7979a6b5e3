package ru.oskelly.concierge.data.model.converter;

import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;
import org.apache.commons.lang3.StringUtils;
import ru.oskelly.concierge.data.model.PurchaseOrder;
import ru.oskelly.concierge.data.model.enums.PurchaseOrderSourceEnum;

/**
 * Конвертер для преобразования PurchaseOrderSourceEnum в строку и обратно.
 * Используется для хранения источника заказа в {@link PurchaseOrder}.
 */
@Converter
public class PurchaseOrderSourceConverter implements AttributeConverter<PurchaseOrderSourceEnum, String> {
    /**
     * Преобразует PurchaseOrderSourceEnum в строку для хранения в базе данных.
     *
     * @param attribute источник заказа
     * @return строковое представление источника заказа
     */
    @Override
    public String convertToDatabaseColumn(PurchaseOrderSourceEnum attribute) {
        return attribute != null ? attribute.getDescription() : null;
    }

    /**
     * Преобразует строку из базы данных в PurchaseOrderSourceEnum.
     *
     * @param dbData строковое представление источника заказа
     * @return источник заказа
     * @throws IllegalArgumentException если значение не соответствует ни одному из перечислений
     */
    @Override
    public PurchaseOrderSourceEnum convertToEntityAttribute(String dbData) throws IllegalArgumentException {
        if (dbData == null) {
            return null;
        }

        for (PurchaseOrderSourceEnum source : PurchaseOrderSourceEnum.values()) {
            if (StringUtils.equalsAnyIgnoreCase(source.name(), dbData)
                || StringUtils.equalsAnyIgnoreCase(source.getDescription(), dbData)) {

                return source;
            }
        }

        throw new IllegalArgumentException("Неизвестное значение: " + dbData);
    }
}
