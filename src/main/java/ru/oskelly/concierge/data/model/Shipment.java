package ru.oskelly.concierge.data.model;

import io.hypersistence.utils.hibernate.type.json.JsonType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EntityListeners;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.Type;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;
import ru.oskelly.concierge.controller.dto.ImageDTO;
import ru.oskelly.concierge.controller.dto.ShimpentSizeDTO;

import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * Товары для заявок(на них формируются предложения)
 */
@Getter
@Setter
@Entity
@Builder
@Table(name = "shipment")
@NoArgsConstructor
@AllArgsConstructor
@EntityListeners(AuditingEntityListener.class)
public class Shipment {
    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "shipment_generator")
    @SequenceGenerator(name = "shipment_generator", sequenceName = "shipment_seq", allocationSize = 1)
    @Column(name = "id", nullable = false)
    private Long id;

    @ManyToOne(optional = false)
    @JoinColumn(name = "purchase_order_id", nullable = false)
    private PurchaseOrder purchaseOrder;

    @Column(name = "category_id")
    private Long categoryId;

    @Column(name = "category_name")
    private String categoryName;

    @Column(name = "brand_id")
    private Long brandId;

    @Column(name = "brand_name")
    private String brandName;

    @Column(name = "brand_transliterate_name")
    private String brandTransliterateName;

    @Column(name = "material_attribute_id")
    private Long materialAttributeId;

    @Column(name = "material_attribute_name")
    private String materialAttributeName;

    @Column(name = "color_attribute_id")
    private Long colorAttributeId;

    @Column(name = "color_attribute_name")
    private String colorAttributeName;

    @CreatedDate
    @Column(name = "created_at")
    private ZonedDateTime createdAt;

    @Column(name = "creator_id")
    private Long creatorId;

    @Column(name = "model_id")
    private Long modelId;

    @Column(name = "model_name")
    private String modelName;

    @Type(JsonType.class)
    @Column(name = "shipment_size")
    private ShimpentSizeDTO shipmentSize;

    @Column(name = "description")
    private String description;

    @Type(JsonType.class)
    @Column(name = "images")
    @Builder.Default
    private List<ImageDTO> images = new ArrayList<>();

    @Type(JsonType.class)
    @Column(name = "links")
    @Builder.Default
    private List<String> links = new ArrayList<>();

    @OneToMany(mappedBy = "shipment", orphanRemoval = true)
    @Builder.Default
    private Set<Offer> offers = new HashSet<>();

    @Column(name = "comment")
    private String comment;
}