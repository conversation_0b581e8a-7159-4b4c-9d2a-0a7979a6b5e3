package ru.oskelly.concierge.data.model;

import io.hypersistence.utils.hibernate.type.json.JsonType;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.EntityListeners;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.NamedAttributeNode;
import jakarta.persistence.NamedEntityGraph;
import jakarta.persistence.NamedEntityGraphs;
import jakarta.persistence.NamedSubgraph;
import jakarta.persistence.OneToMany;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.Type;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;
import ru.oskelly.concierge.controller.dto.ImageDTO;
import ru.oskelly.concierge.data.model.converter.PurchaseOrderSourceConverter;
import ru.oskelly.concierge.data.model.enums.PurchaseOrderSourceEnum;
import ru.oskelly.concierge.data.model.enums.PurchaseOrderStatusEnum;
import ru.oskelly.concierge.data.model.enums.Roles;

import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Getter
@Setter
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "purchase_order")
@EntityListeners(AuditingEntityListener.class)
@NamedEntityGraphs({
        @NamedEntityGraph(
                name = "PurchaseOrder.withShipments",
                attributeNodes = @NamedAttributeNode("shipments")),
        @NamedEntityGraph(
                name = "PurchaseOrder.withShipmentsAndOffers",
                attributeNodes = @NamedAttributeNode(value = "shipments", subgraph = "shipments.offers"),
                subgraphs = @NamedSubgraph(name = "shipments.offers", attributeNodes = @NamedAttributeNode("offers"))
        )
})
public class PurchaseOrder {
    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "purchase_order_generator")
    @SequenceGenerator(name = "purchase_order_generator", sequenceName = "purchase_order_seq", allocationSize = 1)
    @Column(name = "id", nullable = false)
    private Long id;

    @Column(name = "customer_nickname")
    private String customerNickName;

    @Column(name = "customer_phone")
    private String customerPhone;

    @Column(name = "customer_id", nullable = false)
    private Long customerId;

    @Convert(converter = PurchaseOrderSourceConverter.class)
    @Column(name = "source")
    private PurchaseOrderSourceEnum source;

    @Column(name = "description")
    private String description;

    @CreatedDate
    @Column(name = "creation_date")
    private ZonedDateTime creationDate;

    @Column(name = "change_date")
    private ZonedDateTime changeDate;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private PurchaseOrderStatusEnum status;

    @Column(name = "sales_id")
    private Long salesId;

    @Column(name = "sales_fio")
    private String salesFio;

    @Column(name = "sales_nickname")
    private String salesNickName;

    @Enumerated(EnumType.STRING)
    @Column(name = "sales_role")
    private Roles salesRole;

    @Column(name = "sourcer_id")
    private Long sourcerId;

    @Column(name = "reason_return")
    private String reasonReturn;

    @OneToMany(mappedBy = "purchaseOrder", orphanRemoval = true, cascade = CascadeType.ALL)
    @Builder.Default
    private Set<Comment> comments = new HashSet<>();

    @OneToMany(mappedBy = "purchaseOrder", orphanRemoval = true, cascade = CascadeType.ALL)
    @Builder.Default
    private Set<Shipment> shipments = new HashSet<>();

    @Type(JsonType.class)
    @Column(name = "images")
    @Builder.Default
    private List<ImageDTO> images = new ArrayList<>();

    @Type(JsonType.class)
    @Column(name = "orders")
    @Builder.Default
    private List<Long> orders = new ArrayList<>();

    @Column(name = "rejection_reason")
    private String rejectionReason;

    @Column(name = "rejection_description")
    private String rejectionDescription;

    @Column(name = "link")
    private String link;

    @Column(name = "bitrix_deal_id")
    private Long bitrixDealId;

    @Column(name = "chat_id")
    private Long chatId;

    @Column(name = "bitrix_sales_id")
    private Long bitrixSalesId;
}