package ru.oskelly.concierge.event;

import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * Событие изменения статуса заказа на покупку.
 * Используется для уведомления о смене статуса заказа.
 */
@Getter
public class PurchaseOrderStatusChangedEvent extends ApplicationEvent {

    private final Long orderId;

    public PurchaseOrderStatusChangedEvent(Long orderId) {
        super(orderId);
        this.orderId = orderId;
    }
}
