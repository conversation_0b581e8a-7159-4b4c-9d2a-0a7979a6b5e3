package ru.oskelly.concierge.controller.dto.managerdto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;

/**
 * Data Transfer Object (DTO) содержащий информацию о менеджере,
 * ответственном за подбор предложений для заказа.
 * <p>
 * Используется для передачи данных между сервисами и клиентами API.
 */
@Schema(
    description = """
        Информация о менеджере подбора предложения заказа.
        Содержит идентификационные данные менеджера."""
)
@Builder
public record SourcerInfoDTO(
    @Schema(
        description = "Внутренний уникальный идентификатор менеджера в БД",
        example = "789012",
        minimum = "1"
    )
    Long sourcerId,

    @Schema(
        description = "Уникальное имя менеджера в системе",
        example = "best_sourcer_42"
    )
    String nickName,

    @Schema(
        description = "Фамилия, имя и отчество менеджера",
        example = "Петрова Анна Сергеевна",
        maxLength = 255
    )
    String fio,

    @Schema(
        description = "Ссылка на картинку аватара",
        example = "https://example.com/avatar.jpg")
    String urlAvatar,

    @Schema(
        description = "Почта менеджера для поиска в Bitrix",
        example = "<EMAIL>")
    String email
) {
}
