package ru.oskelly.concierge.controller.dto.miniapps;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import ru.oskelly.concierge.controller.dto.ProductConditionDTO;

import java.time.ZonedDateTime;

/**
 * DTO для предложения шопера в miniapps с расширенной информацией о ценах
 */
@Schema(description = "Предложение шопера для мини-приложений")
public record ShopperProposedOfferDTO(
        @Schema(description = "Уникальный идентификатор предложения", example = "123")
        Long id,

        // ToDo заводим локализацию и формируем в зависмости от isSentToCustomer и если есть список orders в ProposedOffer (из 161)
        @Schema(description = "Локализованное описание статуса", 
                example = "Это предложение было выбрано менеджером для отправки покупателю, По данному предложению был сформирован и оплачен заказ")
        String statusDescription,

        @Schema(description = "Дата доставки по предложению",
                example = "2023-12-31T15:00:00+03:00",
                requiredMode = Schema.RequiredMode.REQUIRED)
        @NotNull
        ZonedDateTime deliveryDate,

        @Schema(description = "Срок действия предложения",
                example = "2023-12-15T23:59:59+03:00",
                requiredMode = Schema.RequiredMode.REQUIRED)
        @NotNull
        ZonedDateTime validUntil,

        @Schema(description = "Временная зона", example = "Europe/Moscow")
        String timezone,

        @Schema(description = "Информация о ценах", implementation = PriceInfoDTO.class)
        PriceInfoDTO prices,

        @Schema(description = "Информация о состоянии товара",
                requiredMode = Schema.RequiredMode.REQUIRED)
        @NotNull
        @Valid
        ProductConditionDTO condition,

        @Schema(description = "Флаг наличия чека",
                example = "true",
                requiredMode = Schema.RequiredMode.REQUIRED)
        @NotNull
        Boolean hasReceipt,

        @Schema(description = "Флаг полного комплекта товара",
                example = "true",
                requiredMode = Schema.RequiredMode.REQUIRED)
        @NotNull
        Boolean isCompleteSet,

        @Schema(description = "Комментарий к предложению",
                example = "Товар в отличном состоянии, найден в бутике в Милане")
        String comment
) {
}
