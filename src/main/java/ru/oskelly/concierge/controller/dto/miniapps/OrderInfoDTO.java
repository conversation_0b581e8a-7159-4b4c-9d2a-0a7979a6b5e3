package ru.oskelly.concierge.controller.dto.miniapps;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * DTO для информации о заказе в рамках оффера
 */
@Schema(description = "Информация о заказе")
public record OrderInfoDTO(
        @Schema(description = "Идентификатор заказа", example = "12345")
        Long orderId,

        @Schema(description = "Состояние заказа", example = "PAID")
        String state
) {
}
