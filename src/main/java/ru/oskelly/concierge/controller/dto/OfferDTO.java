package ru.oskelly.concierge.controller.dto;

import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Schema;
import ru.oskelly.concierge.controller.mercaux.dto.ShopperInfoDTO;
import ru.oskelly.concierge.data.model.enums.OfferType;
import ru.oskelly.concierge.service.dto.ComparisonCriterion;

import java.time.ZonedDateTime;
import java.util.Set;

/**
 * DTO для передачи данных о предложении.
 */
@Schema(description = "Информация о предложении продавца")
public record OfferDTO(
        @Schema(description = "Уникальный идентификатор предложения", example = "1")
        Long id,
        @ArraySchema(arraySchema = @Schema(implementation = ComparisonCriterion.class))
        Set<ComparisonCriterion> comparisonCriteria,
        @Schema(description = "Идентификатор заявки", example = "100")
        Long shipmentId,
        @Schema(description = "DTO с информацией о продавца(шопер)", implementation = SellerInfoDTO.class)
        SellerInfoDTO seller,
        @Schema(description = "DTO с информацией о шопере", implementation = ShopperInfoDTO.class)
        ShopperInfoDTO shopper,
        @Schema(description = "Продукт с платформы", implementation = ProductPlatformDTO.class)
        ProductPlatformDTO product,
        @Schema(description = "Тип предложения", implementation = OfferType.class)
        OfferType type,
        @Schema(description = "Тип продавца", example = "INDIVIDUAL")
        String sellerType,
        @Schema(description = "Дата создания предложения", example = "2023-11-25T15:30:00+03:00")
        ZonedDateTime creationDate,
        @Schema(description = "Предложения баера", implementation = BuyerOffers.class)
        BuyerOffers buyerOffers
) {
}