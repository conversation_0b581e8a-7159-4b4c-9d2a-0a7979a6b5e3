package ru.oskelly.concierge.controller.dto.miniapps;

import io.swagger.v3.oas.annotations.media.Schema;
import ru.oskelly.concierge.data.model.enums.Currency;

import java.math.BigDecimal;

/**
 * DTO для информации о ценах с комиссией
 */
@Schema(description = "Информация о ценах с комиссией")
public record PriceInfoDTO(
        @Schema(description = "Валюта закупки", implementation = Currency.class, example = "EUR")
        Currency currency,

        @Schema(description = "Цена в валюте закупки", example = "150.00")
        BigDecimal currencyPrice,

        @Schema(description = "Цена в рублях", example = "13575.00")
        BigDecimal rublePrice,

        @Schema(description = "Цена в валюте с комиссией", example = "172.50")
        BigDecimal currencyPriceWithCommission,

        @Schema(description = "Цена в рублях с комиссией", example = "15611.25")
        BigDecimal localPriceWithCommission
) {
}
