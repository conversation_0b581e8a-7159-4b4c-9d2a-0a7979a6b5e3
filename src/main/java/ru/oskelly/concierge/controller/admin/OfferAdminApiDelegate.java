package ru.oskelly.concierge.controller.admin;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import ru.oskelly.concierge.controller.dto.ProposedOfferDTO;
import ru.oskelly.concierge.controller.dto.SendOffersToClientRequest;
import ru.oskelly.concierge.controller.dto.ShipmentOffersDTO;

import java.util.Set;

@Validated
@Tag(name = "offers-controller", description = "API для взаимодействия с предложениями")
@RequestMapping("/api/v1/offer")
public interface OfferAdminApiDelegate {
    @Operation(
        summary = "Добавление товара с платформы в предложение",
        description = "Добавление товара с платформы в предложение",
            responses = @ApiResponse(responseCode = "200", description = "Товары успешно добавлены в предложение.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = ShipmentOffersDTO.class))
            })
    )
    @PostMapping(
            value = "/products",
            produces = {"application/json"}
    )
    default ResponseEntity<ShipmentOffersDTO> addingProducts(
            @Parameter(name = "userId", description = "ID пользователя", required = true, in = ParameterIn.QUERY)
            @RequestParam(name = "userId") Long userId,
            @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Список товаров с платформы",
                    required = true,
                    content = @Content(mediaType = "application/json", schema = @Schema(implementation = ShipmentOffersDTO.class)))
            @RequestBody @Valid ShipmentOffersDTO shipmentOffers
    ) {
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }

    @Operation(
            summary = "Добавление шоперов в предложение",
            description = "Добавление шоперов в предложение",
            responses = @ApiResponse(responseCode = "200", description = "Шопер(ы) успешно добавлен в предложение.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = ShipmentOffersDTO.class))
            })
    )
    @PostMapping(
            value = "/shoppers",
            produces = {"application/json"}
    )
    default ResponseEntity<ShipmentOffersDTO> addingShoppers(
            @Parameter(name = "userId", description = "ID пользователя", required = true, in = ParameterIn.QUERY)
            @RequestParam(name = "userId") Long userId,
            @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Данные шоперов",
                    required = true,
                    content = @Content(mediaType = "application/json", schema = @Schema(implementation = ShipmentOffersDTO.class)))
            @RequestBody @Valid ShipmentOffersDTO shipmentOffers
    ) {
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }
    @Operation(
            summary = "Удаление товара из предложения",
            description = "Удаление товара из предложения",
            responses = @ApiResponse(responseCode = "200", description = "Товар успешно удален из предложения.")
    )
    @DeleteMapping(
            value = "/{offerId}",
            produces = {"application/json"}
    )
    default ResponseEntity<Void> deleteProduct(
            @Parameter(name = "userId", description = "ID пользователя", required = true, in = ParameterIn.QUERY)
            @RequestParam(name = "userId") Long userId,
            @Parameter(name = "offerId", description = "ID офера", required = true, in = ParameterIn.PATH)
            @PathVariable Long offerId
    ) {
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }

    @Operation(
            summary = "Добавить предложение шопера",
            description = "Добавить предложение шопера",
            responses = @ApiResponse(responseCode = "200", description = "Предложение успешно добавлено",
            content = @Content(mediaType = "application/json", schema = @Schema(implementation = ShipmentOffersDTO.class)))
    )
    @PostMapping(
            value = "/shopperOffer",
            produces = {"application/json"}
    )
    default ResponseEntity<ShipmentOffersDTO> addShopperOffer(
            @Parameter(name = "userId", description = "ID пользователя", required = true, in = ParameterIn.QUERY)
            @RequestParam(name = "userId") Long userId,
            @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Данные по предложению шоперов",
                    required = true,
                    content = @Content(mediaType = "application/json", schema = @Schema(implementation = ShipmentOffersDTO.class)))
            @RequestBody @Valid ShipmentOffersDTO shipmentOffers
    ){
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }

    @Operation(
            summary = "Отправка предложений клиенту в WhatsApp",
            description = "Отправить несколько предложений клиенту в WhatsApp",
            responses = @ApiResponse(
                    responseCode = "200",
                    description = "Предложение успешно добавлено",
                    content = @Content(mediaType = MediaType.APPLICATION_JSON_VALUE)
            )
    )
    @PostMapping(
            value = "/send",
            produces = MediaType.APPLICATION_JSON_VALUE
    )
    default ResponseEntity<Void> sendOffers(
        @Parameter(
                name = "userId",
                description = "ID пользователя",
                required = true,
                in = ParameterIn.QUERY
        )
        @RequestParam(name = "userId") Long userId,

        @io.swagger.v3.oas.annotations.parameters.RequestBody(
                description = "Данные по предложениям для отправки сообщения",
                required = true,
                content = @Content(
                        mediaType = MediaType.APPLICATION_JSON_VALUE,
                        schema = @Schema(implementation = SendOffersToClientRequest.class)
                )
        )
        @RequestBody @Valid SendOffersToClientRequest request
    ){
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }

    @Operation(
            summary = "Получение ProposedOffer по идентификатору Offer",
            description = "Возвращает список ProposedOffer со вложенными сущностями по идентификатору Offer.",
            operationId = "getProposedOffersByOffer",
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Успешный запрос",
                            content = @Content(
                                    mediaType = "application/json",
                                    array = @ArraySchema(schema = @Schema(implementation = ProposedOfferDTO.class))
                            )),
                    @ApiResponse(
                            responseCode = "400",
                            description = "Неверные параметры запроса"
                    )
            }
    )
    @GetMapping(
        value = "/proposedOffers",
        consumes = {"application/json"},
        produces = {"application/json"}
    )
    default ResponseEntity<Set<ProposedOfferDTO>> getProposedByOfferId(
        @Parameter(
            name = "offerId",
            description = "ID оффера для поиска предложений",
            required = true,
            example = "12345")
        @RequestParam(name = "offerId") Long offerId){

        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }
}
