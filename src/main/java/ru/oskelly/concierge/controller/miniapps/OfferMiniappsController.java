package ru.oskelly.concierge.controller.miniapps;

import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;
import ru.oskelly.concierge.common.ContextConstants;
import ru.oskelly.concierge.common.ThreadLocalContext;
import ru.oskelly.concierge.controller.dto.miniapps.OfferDetailsDTO;
import ru.oskelly.concierge.service.OfferService;

/**
 * Контроллер для работы с предложениями в мини-приложениях
 */
@RestController
@RequiredArgsConstructor
public class OfferMiniappsController implements OfferMiniappsApiDelegate {
    
    private final OfferService offerService;

    @Override
    public ResponseEntity<OfferDetailsDTO> getOfferDetails(Long userId, Long offerId) {
        ThreadLocalContext.put(ContextConstants.USER_ID, userId);
        OfferDetailsDTO offerDetails = offerService.getOfferDetails(offerId);
        return ResponseEntity.ok(offerDetails);
    }
}
