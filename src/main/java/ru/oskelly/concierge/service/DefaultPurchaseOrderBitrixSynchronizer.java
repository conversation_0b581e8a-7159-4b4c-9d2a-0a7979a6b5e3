package ru.oskelly.concierge.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import ru.oskelly.concierge.bitrix.dto.DealResponse;
import ru.oskelly.concierge.bitrix.dto.DealStatus;
import ru.oskelly.concierge.config.BitrixSynchronizerProperties;
import ru.oskelly.concierge.data.model.PurchaseOrder;
import ru.oskelly.concierge.data.model.enums.PurchaseOrderStatusEnum;
import ru.oskelly.concierge.data.model.enums.Roles;
import ru.oskelly.concierge.data.repository.PurchaseOrderRepository;
import ru.oskelly.concierge.exception.BitrixClientException;
import ru.oskelly.concierge.exception.BitrixSynchronizationException;
import ru.oskelly.concierge.exception.PurchaseOrderNotFoundException;
import ru.oskelly.concierge.service.dto.BitrixDealAdditionalDto;

import java.util.Optional;

@Slf4j
@Service
@RequiredArgsConstructor
public class DefaultPurchaseOrderBitrixSynchronizer implements PurchaseOrderBitrixSynchronizer {
    private final BitrixService bitrixService;
    private final PurchaseOrderRepository purchaseOrderRepository;
    private final BitrixDealStatusMapper bitrixDealStatusMapper;
    private final PurchaseOrderTransitionService transitionService;
    private final BitrixSynchronizerProperties properties;
    private final PurchaseOrderService purchaseOrderService;

    /**
     * Экспорт PurchaseOrder в сделку в Bitrix.
     *
     * @param orderId          идентификатор заявки
     * @param forcedUpdateData данные для принудительного обновления сделки
     */
    @Override
    public void exportOrder(Long orderId, BitrixDealAdditionalDto forcedUpdateData) {
        if (orderId == null) {
            log.warn("Попытка экспорта сделки с null orderId, пропуск экспорта");
            return;
        }

        log.info("Начало экспорта заявки с ID: {}", orderId);

        try {
            final PurchaseOrder purchaseOrder = purchaseOrderRepository.findById(orderId)
                .orElseThrow(() -> new PurchaseOrderNotFoundException("Purchase order not found with ID: " + orderId, null, orderId, 404));

            final Long dealId = purchaseOrder.getBitrixDealId();
            if (dealId == null) {
                log.info("Заявка {} не связана со сделкой Bitrix (dealId = null), экспорт пропущен", orderId);
                return;
            }

            final DealStatus dealStatus = Optional.ofNullable(forcedUpdateData)
                .map(BitrixDealAdditionalDto::getDealStatus)
                .orElse(bitrixDealStatusMapper.toDealStatus(purchaseOrder.getStatus()));

            if (dealStatus == null) {
                log.warn("Невозможно определить статус сделки для заявки {}, экспорт пропущен", orderId);
                return;
            }

            bitrixService.updateBitrixDealStatus(dealId, dealStatus);
            log.info("Успешно экспортирована заявка {} со статусом сделки Bitrix: {}", orderId, dealStatus.getDescription());
        } catch (BitrixClientException exception) {
            throw new BitrixSynchronizationException(String.format("Failed to export order %d to Bitrix: %s", orderId, exception.getMessage()), orderId, null, exception);
        }
    }

    /**
     * Пакетная синхронизация заявок с Bitrix.
     * Обрабатывает заявки по страницам, экспортируя их в Bitrix.
     * Ограничивает количество обрабатываемых заявок в соответствии с maxBatchSize.
     */
    @Override
    public void exportOrders() {
        int pageNumber = 0;
        int successCount = 0;
        int errorCount = 0;
        Page<PurchaseOrder> page;

        do {
            page = purchaseOrderRepository.findByStatusIn(
                properties.getSynchronizableStatuses(),
                PageRequest.of(pageNumber, properties.getPageSize(), Sort.by(Sort.Direction.ASC, "id")));

            log.info("Обработка страницы {}: {} заявок для экспорта", pageNumber + 1, page.getContent().size());

            for (PurchaseOrder order : page.getContent()) {
                try {
                    log.debug("Экспорт заявки с ID: {}", order.getId());
                    exportOrder(order.getId(), null);
                    successCount++;
                } catch (Exception exception) {
                    errorCount++;
                    log.error("Не удалось экспортировать заявку с ID {}: {}", order.getId(), exception.getMessage(), exception);

                    if (properties.isFailFast()) {
                        log.error("Включен режим быстрого завершения, остановка пакетного экспорта после первой ошибки");
                        throw new BitrixSynchronizationException(
                            "Batch export failed on order: " + order.getId(),
                            order.getId(), null, exception);
                    }
                }
            }

            pageNumber++;
        } while (page.hasNext());

        log.info("Пакетная синхронизация завершена. Успешно: {}, Ошибок: {}", successCount, errorCount);

        if (errorCount > 0 && !properties.isFailFast()) {
            log.warn("Пакетный экспорт завершен с {} ошибками. Проверьте логи для получения подробностей.", errorCount);
        }
    }

    /**
     * Импорт сделки из Bitrix в заявку PurchaseOrder.
     *
     * @param dealId идентификатор сделки в Bitrix
     */
    @Override
    public void importOrder(Long dealId) {
        if (dealId == null) {
            log.warn("Попытка импорта сделки с null bitrix_id, пропуск импорта");
            return;
        }

        log.info("Начало импорта сделки с bitrix_id: {}", dealId);
        final DealResponse deal;
        try {
            deal = bitrixService.getBitrixDeal(dealId);
        } catch (BitrixClientException exception) {
            throw new BitrixSynchronizationException(String.format("Ошибка при получении сделки %s из Bitrix: %s ", dealId, exception.getMessage()), null, dealId, exception);
        }

        final PurchaseOrder purchaseOrder = purchaseOrderRepository.findByBitrixDealId(dealId)
            .orElseThrow(() -> new PurchaseOrderNotFoundException("PurchaseOrder с bitrix_deal_id = " + dealId + " не найден", null, null, 404));

        final PurchaseOrderStatusEnum currentOrderStatus = purchaseOrder.getStatus();
        final DealStatus dealStatus = DealStatus.of(deal.stageId());
        final PurchaseOrderStatusEnum targetOrderStatus = bitrixDealStatusMapper.toOrderStatus(dealStatus);

        log.info("Импорт сделки {}: текущий статус заявки: {}, статус сделки Bitrix: {}, целевой статус заявки: {}",
            dealId,
            currentOrderStatus,
            dealStatus,
            targetOrderStatus);

        if (targetOrderStatus == null || targetOrderStatus.equals(currentOrderStatus)) {
            log.info("Статус заявки не изменился, дальнейшая обработка не требуется");
            return;
        }

        final Long orderId = purchaseOrder.getId();
        purchaseOrderService.eventProcessing(
            orderId,
            transitionService.findTransitionEvent(orderId, currentOrderStatus, targetOrderStatus),
            Roles.CONCIERGE_SALES_ADMIN);
        log.info("Успешно импортирована сделка {} и обновлена заявка {}", dealId, orderId);
    }
}
