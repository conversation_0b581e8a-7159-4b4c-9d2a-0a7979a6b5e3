package ru.oskelly.concierge.service;

import jakarta.annotation.Nullable;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import ru.oskelly.concierge.controller.dto.managerdto.SourcerInfoDTO;
import ru.oskelly.concierge.data.model.enums.PurchaseOrderStatusEnum;
import ru.oskelly.concierge.data.model.enums.Roles;
import ru.oskelly.concierge.data.repository.PurchaseOrderRepository;
import ru.oskelly.concierge.kafka.dto.RequestAvailableSourcerMessage;
import ru.oskelly.concierge.kafka.producer.KafkaMessageProducer;

import java.util.List;
import java.util.Set;

@Slf4j
@Service
@RequiredArgsConstructor
public class DefaultOrderAssignmentSourcerService implements OrderAssignmentSourcerService {
    private final PurchaseOrderService purchaseOrderService;
    private final PurchaseOrderRepository purchaseOrderRepository;
    private final KafkaMessageProducer kafkaMessageProducer;
    private final BitrixService bitrixService;

    @Value("${kafka.topics.sourcer-requests-available-topic.name}")
    private String sourcerRequestsAvailableTopic;

    @Override
    public void requestAvailableSourcer(long orderId) {
        kafkaMessageProducer.sendMessage(
            sourcerRequestsAvailableTopic,
            new RequestAvailableSourcerMessage(orderId, Roles.SOURCER)
        );
    }

    @Override
    public void requestAvailableSourcer(List<Long> orderIds) {
        List<RequestAvailableSourcerMessage> messages = orderIds
            .stream()
            .map(id -> new RequestAvailableSourcerMessage(id, Roles.SOURCER))
            .toList();

        kafkaMessageProducer.sendMessage(sourcerRequestsAvailableTopic, messages);
    }

    @Override
    public void assignToSourcer(long orderId, SourcerInfoDTO sourcerInfo) {
        purchaseOrderService.assignSourcer(orderId, sourcerInfo);
    }

    @Override
    public void redistribute(long fromSourcerId, @Nullable SourcerInfoDTO toSourcerInfo) {
        final List<Long> orderIds = getSourcerOrderIds(fromSourcerId);

        if (toSourcerInfo == null) {
            log.warn("Не указана информация о новом Сорсере для перераспределения заявок от Сорсера с ID: {}", fromSourcerId);
            requestAvailableSourcer(orderIds);
            return;
        }

        purchaseOrderService.assignSourcer(orderIds, toSourcerInfo);
    }

    /**
     * Получение заявок Сорсера в определенных статусах
     * @param sourcerId идентификатор Сорсера
     * @return список идентификаторов заявок
     */
    private List<Long> getSourcerOrderIds(long sourcerId) {
        Set<PurchaseOrderStatusEnum> reassignableStatuses = Set.of(
            PurchaseOrderStatusEnum.AWAITING_SOURCER,
            PurchaseOrderStatusEnum.IN_PROGRESS_SOURCER
        );

        return purchaseOrderRepository.findBySourcerIdAndStatusIn(sourcerId, reassignableStatuses);
    }
}
