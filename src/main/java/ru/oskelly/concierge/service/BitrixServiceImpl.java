package ru.oskelly.concierge.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import ru.oskelly.concierge.bitrix.client.BitrixClient;
import ru.oskelly.concierge.bitrix.client.BitrixOpenLineClient;
import ru.oskelly.concierge.bitrix.dto.BitrixBaseRequest;
import ru.oskelly.concierge.bitrix.dto.BitrixBaseResponse;
import ru.oskelly.concierge.bitrix.dto.BitrixContactDTO;
import ru.oskelly.concierge.bitrix.dto.BitrixFields;
import ru.oskelly.concierge.bitrix.dto.ImopenlinesCrmChatGetLastIdRequest;
import ru.oskelly.concierge.bitrix.dto.SendClientOfferBitrixMessageRequest;
import ru.oskelly.concierge.bitrix.dto.DealFields;
import ru.oskelly.concierge.bitrix.dto.DealGetRequest;
import ru.oskelly.concierge.bitrix.dto.DealResponse;
import ru.oskelly.concierge.bitrix.dto.DealStatus;
import ru.oskelly.concierge.bitrix.dto.DealUpdateRequest;
import ru.oskelly.concierge.bitrix.dto.User;
import ru.oskelly.concierge.bitrix.dto.UserSearchFilter;
import ru.oskelly.concierge.exception.BitrixClientException;
import ru.oskelly.concierge.exception.BitrixDealException;
import ru.oskelly.concierge.exception.BitrixUserException;

import java.util.List;
import java.util.Map;

import static ru.oskelly.concierge.bitrix.dto.BitrixFields.CONTACT;

@Slf4j
@Service
@RequiredArgsConstructor
public class BitrixServiceImpl implements BitrixService {

    private final BitrixClient bitrixClient;
    private final BitrixOpenLineClient bitrixOpenLineClient;

    @Override
    public BitrixBaseResponse<Long> createDeal(BitrixBaseRequest request) {
        BitrixBaseResponse<Long> response;
        try {
            log.info("Создание сделки в Битрикс...");
            response = bitrixClient.createDeal(request);
            log.info("Сделка успешно создана: {}", response.getResult());
        } catch (BitrixClientException exception) {
            throw new BitrixClientException("Ошибка при создании сделки в Битрикс");
        }

        return response;
    }

    @Override
    public BitrixBaseResponse<List<BitrixContactDTO>> listContacts(String phone) {
        BitrixBaseResponse<List<BitrixContactDTO>> response;
        try {
            log.info("Получение контакта в Битрикс по номеру телефона...");
            response = bitrixClient.listContacts(buildListContactsRequest(phone));
        } catch (BitrixClientException exception) {
            throw new BitrixClientException("Ошибка при попытке получить контакт в Битрикс");
        }

        return response;
    }

    @Override
    public BitrixBaseResponse<Long> createContact(BitrixBaseRequest request) {
        BitrixBaseResponse<Long> response;
        try {
            log.info("Создание контакта в Битрикс...");
            response = bitrixClient.createContact(request);
            log.info("Контакт успешно создан: {}", response.getResult());
        } catch (BitrixClientException exception) {
            throw new BitrixClientException("Ошибка при создании контакта в Битрикс");
        }

        return response;
    }

    @Override
    public BitrixBaseResponse<Long> getLastChatId(Long entityId) {
        var request = new ImopenlinesCrmChatGetLastIdRequest(CONTACT, entityId);
        try {
            log.info("Получение chatId из битрикс...");
            return bitrixOpenLineClient.getLastChatId(request);
        } catch (Exception e) {
            log.warn("Не удалось получить chatId");
            BitrixBaseResponse<Long> empty = new BitrixBaseResponse<>();
            empty.setResult(null);
            return empty;
        }
    }

    @Override
    public void sendMessageByChatId(Long chatId, String message) {
        var request = new SendClientOfferBitrixMessageRequest(chatId, message);
        try {
            log.info("Отправка сообщения в чат, chatId - {}", chatId);
            bitrixOpenLineClient.sendMessageByChatId(request);
        } catch (Exception e) {
            throw new BitrixClientException("Не удалось отправить сообщение");
        }
    }

    @Override
    public void sendMessageByPhoneNumber(Long phone, String message) {
        var request = new SendClientOfferBitrixMessageRequest(phone, message);
        try {
            log.info("Отправка сообщения в чат, phone - {}", phone);
            bitrixOpenLineClient.sendMessageByPhoneNumber(request);
        } catch (Exception e) {
            throw new BitrixClientException("Не удалось отправить сообщение");
        }
    }

    @Override
    public void sendOffersToBitrixOpenLine(String phone, String text, Long chatId) {
        if (chatId != null) {
            sendMessageByChatId(chatId, text);
        } else {
            String normalizedPhone = PhoneUtils.normalizePhone(phone);
            sendMessageByPhoneNumber(Long.valueOf(normalizedPhone),text);
        }
    }

    @Override
    public void updateBitrixDealStatus(Long dealId, DealStatus dealStatus) {
        DealUpdateRequest request = DealUpdateRequest.builder()
                .id(dealId)
                .fields(DealFields.builder()
                        .stageId(dealStatus.getCode())
                        .build())
                .build();

        BitrixBaseResponse<String> response = bitrixClient.updateDeal(request);

        if (!Boolean.parseBoolean(response.getResult())) {
            throw new BitrixClientException("Deal update returned false result for deal: " + dealId);
        }
    }

    @Override
    public DealResponse getBitrixDeal(Long dealId) {
        return bitrixClient.getDeal(new DealGetRequest(dealId)).getResult();
    }

    public Long getCrmEntityId(String phone) {
        BitrixBaseResponse<List<BitrixContactDTO>> contactsResponse = listContacts(phone);
        if (contactsResponse == null
                || contactsResponse.getResult() == null
                || contactsResponse.getResult().isEmpty()) {
            log.info("listContacts вернул null или пустой список, телефон: {}", phone);
            return null;
        }

        BitrixContactDTO crmEntity = contactsResponse.getResult().get(0);
        if (crmEntity == null || crmEntity.getId() == null) {
            log.info("ID сущности null, телефон: {}", phone);
            return null;
        }

        return crmEntity.getId();
    }

    private BitrixBaseRequest buildListContactsRequest(String phone) {
        return BitrixBaseRequest.builder()
                .filter(Map.of(BitrixFields.PHONE, phone))
                .select(List.of(BitrixFields.ALL, BitrixFields.ALL_CUSTOM_FIELDS))
                .build();
    }

    @Override
    public User findUserByEmail(String email) {
        log.info("Поиск пользователя в Bitrix по email: {}", email);
        try {
            final UserSearchFilter filter = UserSearchFilter.builder().email(email).build();
            final BitrixBaseResponse<List<User>> response = bitrixClient.findUsers(filter);

            List<User> users = response.getResult();

            if (CollectionUtils.isEmpty(users)) {
                throw new BitrixUserException("Пользователь не был найден в Bitrix. Email: " + email, null, null, HttpStatus.NOT_FOUND.value());
            }

            return users.getFirst();
        } catch (BitrixClientException | BitrixUserException exception) {
            log.error("Возникла ошибка при поиске пользователя {} в Bitrix: {}", email, exception.getMessage(), exception);
            throw exception;
        }
    }

    @Override
    public void assignUserToDeal(Long userId, Long dealId) {
        if (dealId == null) {
            log.warn("Отсутствует идентификатор сделки.");
            return;
        }

        log.info("Назначение пользователя {} ответственным на сделку {}", userId, dealId);
        try {
            DealUpdateRequest dealUpdateRequest = DealUpdateRequest.builder()
                .id(dealId)
                .fields(DealFields.builder()
                    .assignedById(userId)
                    .build())
                .build();
            BitrixBaseResponse<String> response = bitrixClient.updateDeal(dealUpdateRequest);

            if (!Boolean.parseBoolean(response.getResult())) {
                throw new BitrixDealException("Не удалось назначить пользователя на сделку. Bitrix UserID: " + userId, null, null, HttpStatus.BAD_GATEWAY.value());
            }
        } catch (BitrixClientException | BitrixDealException exception) {
            log.error("Возникла ошибка при установке пользователя {} ответственным на сделку {}: {}", userId, dealId, exception.getMessage(), exception);
            throw exception;
        }
    }
}
