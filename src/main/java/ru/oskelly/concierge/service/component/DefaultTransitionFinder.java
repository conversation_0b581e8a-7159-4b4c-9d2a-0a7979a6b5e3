package ru.oskelly.concierge.service.component;

import org.springframework.statemachine.StateMachine;
import org.springframework.statemachine.trigger.EventTrigger;
import org.springframework.statemachine.trigger.Trigger;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Класс для поиска переходов состояний в StateMachine.
 *
 * @param <S> Status
 * @param <E> Event
 */
public class DefaultTransitionFinder<S, E> implements TransitionFinder<S, E> {
    /**
     * Граф состояний, где ключ - начальное состояние,
     */
    private final Map<S, List<TransitionLink<S, E>>> stateGraph = new HashMap<>();

    /**
     * Конструктор, который инициализирует граф состояний на основе переданной StateMachine.
     *
     * @param stateMachine StateMachine, из которого извлекаются переходы
     */
    public DefaultTransitionFinder(StateMachine<S, E> stateMachine) {
        stateMachine.getTransitions()
            .forEach(transition -> {
                final Trigger<S, E> trigger = transition.getTrigger();
                if (trigger instanceof EventTrigger<S, E> eventTrigger) {
                    E event = eventTrigger.getEvent();
                    if (event == null) {
                        return;
                    }

                    S source = transition.getSource().getId();
                    S target = transition.getTarget().getId();

                    stateGraph.computeIfAbsent(source, k -> new ArrayList<>())
                        .add(new TransitionLink<>(target, event));
                }
            });
    }

    /**
     * Возвращает одно событие, которое переводит из fromStatus в toStatus,
     * или выбрасывает исключение если переход отсутствует или неоднозначен.
     *
     * @param fromStatus начальный статус
     * @param toStatus   конечный статус
     * @return событие перехода
     * @throws IllegalStateException если переход не найден или неоднозначен
     */
    public E findEvent(S fromStatus, S toStatus) throws IllegalStateException {
        final List<TransitionLink<S, E>> transitions = stateGraph.getOrDefault(fromStatus, List.of())
            .stream()
            .filter(link -> link.target.equals(toStatus))
            .toList();

        if (transitions.isEmpty()) {
            throw new IllegalStateException("Переход из %s в %s не найден".formatted(fromStatus, toStatus));
        }

        if (transitions.size() > 1) {
            throw new IllegalStateException("Найдено несколько переходов из %s в %s".formatted(fromStatus, toStatus));
        }

        return transitions.getFirst().event;
    }

    /**
     * Вспомогательный класс для хранения связей между состояниями и событиями переходов.
     *
     * @param target целевое состояние
     * @param event событие перехода
     */
    private record TransitionLink<S, E>(S target, E event) {
    }
}
