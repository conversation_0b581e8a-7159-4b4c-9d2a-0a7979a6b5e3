package ru.oskelly.concierge.service;

import jakarta.annotation.Nullable;
import ru.oskelly.concierge.controller.dto.AvailableSalesDTO;
import ru.oskelly.concierge.controller.dto.SalesInfoDTO;

import java.util.List;

/**
 * Сервис распределения заявок на Сейлза
 */
public interface OrderAssignmentSalesService {

    /**
     * Запросить свободного Сейлза для заявки
     * @param orderId идентификатор заявки
     */
    void requestAvailableSales(long orderId);

    /**
     * Запросить свободных Сейлзов для заявок
     * @param orderIds идентификаторы заявок
     */
    void requestAvailableSales(List<Long> orderIds);

    /**
     * Назначить заявку Сейлзу
     * @param orderId идентификатор заявки
     * @param availableSales данные Сейлза
     */
    void assignToSales(long orderId, AvailableSalesDTO availableSales);

    /**
     * Распределить заявки одного сотрудника (Сейлза) на другого.
     * Заявки Сейлза распределяются на указанного Админа.
     * Заявки Админа распределяются на Сейлзов из очереди.
     * @param fromSalesId заявки от Сейлза (ID)
     * @param toSalesInfo заявки Сейлзу (Админу)
     */
    void redistribute(long fromSalesId, @Nullable SalesInfoDTO toSalesInfo);
}
