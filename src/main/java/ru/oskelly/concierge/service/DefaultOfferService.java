package ru.oskelly.concierge.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ru.oskelly.concierge.bitrix.client.BitrixClient;
import ru.oskelly.concierge.bitrix.dto.BitrixBaseResponse;
import ru.oskelly.concierge.bitrix.dto.DealFields;
import ru.oskelly.concierge.bitrix.dto.DealUpdateRequest;
import ru.oskelly.concierge.bitrix.dto.OfferCreateRequest;
import ru.oskelly.concierge.bitrix.dto.DealStatus;
import ru.oskelly.concierge.common.ContextConstants;
import ru.oskelly.concierge.common.ThreadLocalContext;
import ru.oskelly.concierge.controller.dto.OfferDTO;
import ru.oskelly.concierge.controller.dto.ProposedOfferDTO;
import ru.oskelly.concierge.controller.dto.SendOffersToClientRequest;
import ru.oskelly.concierge.controller.dto.ShipmentOffersDTO;
import ru.oskelly.concierge.controller.dto.miniapps.OfferDetailsDTO;
import ru.oskelly.concierge.data.mapper.OfferMapper;
import ru.oskelly.concierge.data.mapper.OfferDetailsMapper;
import ru.oskelly.concierge.data.mapper.ProposedOfferMapper;
import ru.oskelly.concierge.data.model.Offer;
import ru.oskelly.concierge.data.model.PersonalShopper;
import ru.oskelly.concierge.data.model.ProposedOffer;
import ru.oskelly.concierge.data.model.PurchaseOrder;
import ru.oskelly.concierge.data.model.Shipment;
import ru.oskelly.concierge.data.model.enums.OfferType;
import ru.oskelly.concierge.data.model.enums.PurchaseOrderStatusEnum;
import ru.oskelly.concierge.data.repository.OfferRepository;
import ru.oskelly.concierge.data.repository.PersonalShopperRepository;
import ru.oskelly.concierge.data.repository.ProposedOfferRepository;
import ru.oskelly.concierge.data.repository.PurchaseOrderRepository;
import ru.oskelly.concierge.data.repository.ShipmentRepository;
import ru.oskelly.concierge.exception.OfferCountException;
import ru.oskelly.concierge.exception.OfferNotFoundException;
import ru.oskelly.concierge.exception.OfferTypeException;
import ru.oskelly.concierge.exception.ProposedOfferNotFoundException;
import ru.oskelly.concierge.exception.ProposedOfferValidationException;
import ru.oskelly.concierge.exception.PurchaseOrderNotFoundException;
import ru.oskelly.concierge.exception.ShipmentNotFoundException;
import ru.oskelly.concierge.exception.ValidationException;
import ru.oskelly.concierge.kafka.dto.SendClientOfferWhatsAppMessage;
import ru.oskelly.concierge.kafka.producer.KafkaMessageProducer;
import ru.oskelly.concierge.service.dto.BitrixDealAdditionalDto;

import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import static ru.oskelly.concierge.data.model.enums.PurchaseOrderSourceEnum.SALES_APP;
import static ru.oskelly.concierge.data.model.enums.PurchaseOrderStatusEnum.AWAITING_SEND_TO_CLIENT;

@Slf4j
@Service
@RequiredArgsConstructor
public class DefaultOfferService implements OfferService {
    @Value("${concierge.offer.max-count-product-platform:5}")
    private int maxCountProductPlatform;
    @Value("${concierge.offer.max-count-offer:2}")
    private int maxCountOffer;
    @Value("${kafka.topics.offer-message-client-send-request.name}")
    private String offerMessageClientSendRequestTopic;
    @Value("${monolith.host}")
    private String monolithHost;

    private final OfferRepository offerRepository;
    private final OfferMapper offerMapper;
    private final OfferDetailsMapper offerDetailsMapper;
    private final ProposedOfferMapper proposedOfferMapper;
    private final ShipmentRepository shipmentRepository;
    private final PersonalShopperRepository shopperRepository;
    private final BitrixClient bitrixClient;
    private final ProposedOfferRepository proposedOfferRepository;
    private final PurchaseOrderRepository purchaseOrderRepository;
    private final KafkaMessageProducer kafkaMessageProducer;
    private final ConciergeStateMachineService conciergeStateMachineService;
    private final PersonalShopperRepository personalShopperRepository;
    private final BitrixService bitrixService;
    private final OfferStatusService offerStatusService;
    private final PurchaseOrderService purchaseOrderService;
    private final PurchaseOrderBitrixSynchronizer synchronizer;

    @Override
    @Transactional
    public ShipmentOffersDTO addingProducts(ShipmentOffersDTO shipmentOffers) {
        if (!isCheckNewCountProduct(shipmentOffers)) {
            throw new OfferCountException("Кол-во добавляемых продуктов больше или есть одинаковые продукты -> " + maxCountProductPlatform, null, null, 400);
        }
        var userId = ThreadLocalContext.get(ContextConstants.USER_ID, Long.class);
        List<Offer> offersForProduct = addingOffers(shipmentOffers, userId, OfferType.PLATFORM_PRODUCT);
        return ShipmentOffersDTO.builder()
                .shipmentId(shipmentOffers.shipmentId())
                .offers(offerMapper.toDTOList(offersForProduct, personalShopperRepository))
                .build();
    }

    @Override
    @Transactional
    public ShipmentOffersDTO addingShoppers(ShipmentOffersDTO shipmentOffers) {
        var userId = ThreadLocalContext.get(ContextConstants.USER_ID, Long.class);
        List<Offer> offersForShopper = addingOffers(shipmentOffers, userId, OfferType.BUYER_OFFER);
        ShipmentOffersDTO shipmentOffersDTO = ShipmentOffersDTO.builder()
                .offers(offerMapper.toDTOList(offersForShopper, personalShopperRepository))
                .shipmentId(shipmentOffers.shipmentId())
                .build();

        Long bitrixDealId = offersForShopper.getFirst().getShipment().getPurchaseOrder().getBitrixDealId();
        Map<Long, String> shopperIdToBitrixId = getSellerIdToBitrixId(offersForShopper);

        List<OfferCreateRequest> requests = shipmentOffersDTO.offers().stream()
                .map(offer -> offerMapper.toCreateEmptyRequest(offer, bitrixDealId, shopperIdToBitrixId.get(offer.seller().sellerId())))
                .toList();

        sendOffersToBitrix(offersForShopper, requests);

        return shipmentOffersDTO;
    }

    private List<Offer> addingOffers(ShipmentOffersDTO shipmentOffers, Long userId, OfferType type) {
        var existsShipment = shipmentRepository.findById(shipmentOffers.shipmentId())
                .orElseThrow(() -> new ShipmentNotFoundException("Предложение с id " + shipmentOffers.shipmentId() + " не найдено.", null, null, 400));

        if (existsShipment.getPurchaseOrder() == null ||
                existsShipment.getPurchaseOrder().getStatus() != PurchaseOrderStatusEnum.IN_PROGRESS_SOURCER) {
            throw new PurchaseOrderNotFoundException("Заявка находится в недопустимом статусе.", null, null, 400);
        }

        final boolean isFirstBuyerOffer = existsShipment.getOffers().isEmpty() && type.equals(OfferType.BUYER_OFFER);

        // Заготовка оферов
        List<Offer> offers = new ArrayList<>();
        shipmentOffers.offers().forEach(offerDTO -> {
            var offer = Offer.builder()
                    .userId(userId)
                    .sellerType(offerDTO.sellerType())
                    .shipment(existsShipment)
                    .type(type)
                    .sellerId(offerDTO.seller() != null ? offerDTO.seller().sellerId() : null)
                    .build();

            if (type.equals(OfferType.BUYER_OFFER)) {
                offer.setProposedOffers(createOrUpdateProposedOffer(shipmentOffers, offer));
            } else if (type.equals(OfferType.PLATFORM_PRODUCT)) {
                if (offerDTO.product() != null) {
                    offer.setProductId(offerDTO.product().productId());
                }
            }

            offer.setUserId(ThreadLocalContext.get(ContextConstants.USER_ID, Long.class));
            offers.add(offer);
        });

        List<Offer> offerList = offerRepository.saveAll(offers);

        // Синхронизация статуса заявки с Bitrix при первом добавлении предложений от Байера
        if (isFirstBuyerOffer) {
            syncDealStatusOnBuyerOfferAdded(existsShipment.getPurchaseOrder());
        }

        return offerList;
    }

    private Set<ProposedOffer> createOrUpdateProposedOffer(ShipmentOffersDTO shipmentOffers, Offer offer) {
        return shipmentOffers.offers().stream()
                .filter(offerDTO -> offerDTO.buyerOffers() != null &&
                        offerDTO.buyerOffers().proposedOffers() != null)
                .map(offerDTO -> proposedOfferMapper.toEntityList(
                        offerDTO.buyerOffers().proposedOffers(), offer))
                .flatMap(Set::stream)
                .collect(Collectors.toSet());
    }

    @Override
    @Transactional
    public Void deleteProduct(Long offerId) {
        offerRepository.findById(offerId)
                .orElseThrow(() -> new OfferNotFoundException("Оффер с id " + offerId + " не найден.", null, null, 400));
        offerRepository.deleteById(offerId);
        return null;
    }

    /**
     * Добавляет предложение покупателя (shopper offer) к отгрузке (shipment).
     * <p>
     * Выполняется валидация входных данных и бизнес-правил.
     *
     * @param shipmentOffers DTO с предложениями для отгрузки {@link ShipmentOffersDTO}
     * @return список добавленных или обновленных предложений {@link Offer}
     */
    @Override
    @Transactional
    public ShipmentOffersDTO addShopperOffer(ShipmentOffersDTO shipmentOffers) {
        final Shipment existsShipment = shipmentRepository.findById(shipmentOffers.shipmentId())
                .orElseThrow(() -> new ShipmentNotFoundException(
                        "Предложение с id " + shipmentOffers.shipmentId() + " не найдено.", null, null, 400));

        if (existsShipment.getPurchaseOrder() == null ||
                !PurchaseOrderStatusEnum.IN_PROGRESS_SOURCER.equals(existsShipment.getPurchaseOrder().getStatus())) {
            throw new PurchaseOrderNotFoundException("Заявка находится в недопустимом статусе.", null, null, 400);
        }

        if (shipmentOffers.offers() == null || shipmentOffers.offers().isEmpty()) {
            throw new ShipmentNotFoundException("Отсутствуют предложения для добавления", null, null, 400);
        }

        final List<Offer> savedOffers = new ArrayList<>();

        for (OfferDTO offerDTO : shipmentOffers.offers()) {
            final Offer existingOffer = getAndValidateExistingOffer(offerDTO);

            // Обновляем существующее предложение, сохраняя данные, которые не должны изменяться
            offerMapper.updateToEntity(offerDTO, existingOffer);
            existingOffer.setId(existingOffer.getId());
            existingOffer.setShipment(existsShipment);

            // Дополнительная логика для сохранения полей, которые не должны обновляться
            Optional.ofNullable(existingOffer.getProposedOffers())
                    .ifPresent(proposedOffers -> proposedOffers.forEach(proposedOffer -> proposedOffer.setOffer(existingOffer)));
            existingOffer.setUserId(ThreadLocalContext.get(ContextConstants.USER_ID, Long.class));

            savedOffers.add(existingOffer);
        }

        final List<OfferDTO> savedOfferDTOs = offerRepository.saveAll(savedOffers)
                .stream()
                .map(offer -> offerMapper.toDTO(offer, personalShopperRepository))
                .toList();

        return new ShipmentOffersDTO(shipmentOffers.shipmentId(), savedOfferDTOs);
    }

    @Override
    public void sendOffersToClient(SendOffersToClientRequest request) {
        Long orderId = request.orderId();
        PurchaseOrder purchaseOrder = getPurchaseOrder(orderId);

        var stateMachine = conciergeStateMachineService.getStateMachine(orderId);
        PurchaseOrderStatusEnum currentStatus = stateMachine.getState().getId();

        if (!currentStatus.equals(AWAITING_SEND_TO_CLIENT)) {
            throw new ValidationException(
                    "Отправлять предложения можно только в статусе " + AWAITING_SEND_TO_CLIENT, null, null, null, 400
            );
        }

        List<Offer> offers = findOffers(request.offerIds());
        validateOffersBelongToOrder(offers, orderId);

        List<ProposedOffer> proposedOffers = findProposedOffers(request.proposedOfferIds());
        validateProposedOffersBelongToOrder(proposedOffers, orderId);

        String text = buildOfferMessageText(request.message(), offers, proposedOffers);
        var userId = ThreadLocalContext.get(ContextConstants.USER_ID, Long.class);
        String phone = purchaseOrder.getCustomerPhone();
        Long salesId = purchaseOrder.getSalesId();

        if (!StringUtils.hasText(phone)) {
            throw new ValidationException("Телефон клиента не может быть пустым", null, null, null, 400);
        }

        List<Long> offerIds = offers
                .stream()
                .map(Offer::getId)
                .toList();

        List<Long> proposedOfferIds = proposedOffers
                .stream()
                .map(ProposedOffer::getId)
                .toList();

        if (purchaseOrder.getSource() == SALES_APP) {
            var message = new SendClientOfferWhatsAppMessage(salesId, userId, orderId, offerIds, proposedOfferIds, phone, text);
            kafkaMessageProducer.sendMessage(offerMessageClientSendRequestTopic, message);
        } else {
            Long entityId = bitrixService.getCrmEntityId(phone);
            Long chatId = bitrixService.getLastChatId(entityId).getResult();
            purchaseOrderService.updateChatId(purchaseOrder.getId(), chatId);
            bitrixService.sendOffersToBitrixOpenLine(phone, text, chatId);
            offerStatusService.setOffersAsSentToCustomer(offerIds, proposedOfferIds);
        }
    }

    private void sendOffersToBitrix(List<Offer> offersForShopper, List<OfferCreateRequest> requests) {
        for (int i = 0; i < requests.size(); i++) {
            Offer offer = offersForShopper.get(i);
            OfferCreateRequest request = requests.get(i);
            try {
                log.info("Id шоппера: " + request.fields().companyId());
                BitrixBaseResponse<Integer> offerResponse = bitrixClient.createOffer(request);
                log.info("Оффер отправлен в битрикс: {}", offerResponse.getResult());

                offer.setBitrixId(offerResponse.getResult().longValue());
            } catch (Exception ex) {
                log.error("Ошибка при отправке офферов в Bitrix {}: {}", request, ex.getMessage(), ex);
            }
        }
    }

    private Map<Long, String> getSellerIdToBitrixId(List<Offer> offersForShopper) {
        if (offersForShopper == null || offersForShopper.isEmpty()) {
            return Collections.emptyMap();
        }

        List<Long> shopperIds = offersForShopper.stream()
                .filter(offer -> offer != null && offer.getSellerId() != null)
                .map(Offer::getSellerId)
                .distinct()
                .toList();

        return shopperRepository.findAllByUserIdIn(shopperIds)
                .stream()
                .filter(ps -> ps != null && ps.getUserId() != null && ps.getBitrixId() != null)
                .collect(Collectors.toMap(
                        PersonalShopper::getUserId,
                        PersonalShopper::getBitrixId,
                        (v1, v2) -> v1
                ));
    }

    /**
     * Ищет и валидирует существующее предложение по идентификатору.
     * <p>
     * Валидации:
     * <ul>
     *     <li>Наличие существующего {@code Offer}.</li>
     *     <li>Количество добавляемых предложений не превышает {@link DefaultOfferService#maxCountOffer}.</li>
     *     <li>Тип предложения соответствует ожидаемому (добавлять {@code proposedOffer} можно только к типу {@link OfferType#BUYER_OFFER}).</li>
     *     <li>Корректность дат каждого {@code proposedOffer}.</li>
     *     <li>Заполнение полей комиссии.</li>
     * </ul>
     *
     * @param offerDTO DTO входящего запроса {@link OfferDTO}
     * @return найденное провалидированное предложение {@link Offer}
     */
    private Offer getAndValidateExistingOffer(OfferDTO offerDTO) {
        if (offerDTO.id() == null) {
            throw new OfferNotFoundException("Не передан offerId.", 400);
        }
        final Offer existingOffer = offerRepository.findById(offerDTO.id())
                .orElseThrow(() -> new OfferNotFoundException("Оффер с id " + offerDTO.id() + " не найден.", 400)); // новые не создаем, добавляем только к существующим

        if (!isCheckCountProposedOffer(existingOffer, offerDTO)) {
            throw new OfferCountException("Кол-во добавляемых оферов больше " + maxCountOffer, 400);
        }

        if (!Objects.equals(existingOffer.getType(), OfferType.BUYER_OFFER)
                && !CollectionUtils.isEmpty(offerDTO.buyerOffers().proposedOffers())) {

            throw new OfferTypeException("Невозможно добавить proposedOffers в offer с id " + existingOffer.getId() + ". Неверный тип оффера.", 400);
        }

        offerDTO.buyerOffers().proposedOffers()
                .forEach(this::validateProposedOffer);

        return existingOffer;
    }

    /**
     * Проверяет, не превышает ли количество предложений типа {@code proposedOffer} допустимое значение.
     * <p>
     * Учитывает уже сохранённые предложения и новые, которые будут добавлены или обновлены.
     * Если количество превышает {@code maxCountOffer}, выбрасывает {@link OfferCountException}.
     *
     * @param existingOffer существующее предложение {@link Offer}, к которому добавляются новые предложения
     * @param offerDTO      DTO предложения с новыми предложениями {@link OfferDTO}
     * @return {@code true}, если количество предложений не превышает лимит, иначе {@code false}
     */
    private boolean isCheckCountProposedOffer(Offer existingOffer, OfferDTO offerDTO) {
        if (CollectionUtils.isEmpty(offerDTO.buyerOffers().proposedOffers())) {
            return true; // Нет предложений для проверки
        }

        final Set<ProposedOffer> currentProposedOffers = existingOffer.getProposedOffers() != null
                ? existingOffer.getProposedOffers()
                : Set.of();

        final List<ProposedOfferDTO> newOffers = offerDTO.buyerOffers().proposedOffers()
                .stream()
                .filter(proposedOffer -> proposedOffer.id() == null)
                .toList();
        final List<ProposedOfferDTO> updatingOffers = offerDTO.buyerOffers().proposedOffers()
                .stream()
                .filter(proposedOffer -> proposedOffer.id() != null)
                .toList();

        // Проверяем, что все обновляемые предложения существуют в базе
        final Set<Long> existingProposedOfferIds = currentProposedOffers
                .stream()
                .map(ProposedOffer::getId)
                .collect(Collectors.toSet());
        updatingOffers.stream()
                .filter(updatingOffer -> !existingProposedOfferIds.contains(updatingOffer.id()))
                .findFirst()
                .ifPresent(updatingOffer -> {
                    throw new ProposedOfferNotFoundException(
                            "Предложение с id " + updatingOffer.id() + " не найдено среди существующих предложений оффера.", 400);
                });

        // Подсчитываем итоговое количество предложений после операции:
        // текущие сохраненные + новые (обновляемые не увеличивают общее количество)
        final int totalOffersAfterOperation = currentProposedOffers.size() + newOffers.size();

        return totalOffersAfterOperation <= maxCountOffer;
    }

    private boolean isCheckNewCountProduct(ShipmentOffersDTO shipmentOffers) {
        boolean result = false;
        var shipmentId = shipmentOffers.shipmentId();
        List<Offer> offers = offerRepository.findByShipment_IdAndProductIdNotNull(shipmentId);
        List<OfferDTO> offerDtos = shipmentOffers.offers()
                .stream()
                .filter(offerDTO -> offerDTO.product() != null)
                .filter(offerDTO -> offerDTO.product().productId() != null)
                .toList();

        // Количество добавляемых продуктов не должно превышать в сумме с имеющимися существующее ограничение
        if (offers.size() + offerDtos.size() <= maxCountProductPlatform) {
            result = true;
        }
        Set<Long> dtoProductIds = offerDtos.stream()
                .map(offerDTO -> offerDTO.product().productId())
                .collect(Collectors.toSet());

        boolean hasMatchingProduct = offers.stream()
                .noneMatch(offer -> dtoProductIds.contains(offer.getProductId()));
        return result && hasMatchingProduct;
    }

    /**
     * Валидирует параметры {@link ProposedOfferDTO}.
     * <p>
     * Проверяет корректность дат доставки и срока действия, а также наличие комиссии при необходимости.
     * В случае некорректных данных выбрасывает {@link ProposedOfferValidationException}.
     *
     * @param proposedOffer DTO предложения {@link ProposedOfferDTO}
     * @throws ProposedOfferValidationException если параметры предложения некорректны
     */
    private void validateProposedOffer(ProposedOfferDTO proposedOffer) {
        final ZonedDateTime now = ZonedDateTime.now();
        if (proposedOffer.deliveryDate().isBefore(now)) {
            throw new ProposedOfferValidationException(
                    "Дата доставки для предложения с ID " + proposedOffer.id() + " не может быть раньше текущей даты.",
                    null, null, 400);
        }
        if (proposedOffer.validUntil().isBefore(now)) {
            throw new ProposedOfferValidationException(
                    "Поле \"Действительно до\" для предложения с ID " + proposedOffer.id() + " не может быть раньше текущей даты.",
                    null, null, 400);
        }
        if (proposedOffer.hasCustomCommission() && proposedOffer.commission() == null) {
            throw new ProposedOfferValidationException(
                    "Не установлена комиссия для предложения с ID  " + proposedOffer.id(),
                    null, null, 400);
        }
    }

    /**
     * Построение сообщения для клиента по предложениям
     * @param text текст сообщения
     * @param offers предложения
     * @return итоговое сообщение
     */
    private String buildOfferMessageText(String text, List<Offer> offers, List<ProposedOffer> proposedOffers) {
        List<String> offerProductLinks = offers
            .stream()
            .map(Offer::getProductId)
            .map(productId -> String.format("- %s/products/%s", monolithHost, productId))
            .collect(Collectors.toList());

        List<String> proposedOfferProductLinks = proposedOffers
            .stream()
            .map(ProposedOffer::getProposedProductId)
            .map(productId -> String.format("- %s/products/%s", monolithHost, productId))
            .toList();

        offerProductLinks.addAll(proposedOfferProductLinks);
        String productLinks = String.join("\n", offerProductLinks);

        return String.format("%s%n%n%s", text, productLinks);
    }

    /**
     * Получение сущности заявки
     * @param orderId идентификатор заявки
     * @return entity
     */
    private PurchaseOrder getPurchaseOrder(Long orderId) {
        return purchaseOrderRepository
            .findById(orderId)
            .orElseThrow(() -> new PurchaseOrderNotFoundException(
                String.format("Заявка с ID %s не найдена", orderId), null, orderId, 404)
            );
    }

    /**
     * Поиск предложений по списку идентификаторов
     * @param offerIds идентификаторы предложений
     * @return список сущностей
     */
    private List<Offer> findOffers(List<Long> offerIds) {
        if (CollectionUtils.isEmpty(offerIds)) {
            return Collections.emptyList();
        }

        List<Offer> offers = offerRepository.findAllById(offerIds);

        Set<Long> foundOfferIds = offers.stream()
            .map(Offer::getId)
            .collect(Collectors.toSet());

        List<Long> notFoundOfferIds = offerIds.stream()
            .filter(id -> !foundOfferIds.contains(id))
            .toList();

        if (!notFoundOfferIds.isEmpty()) {
            throw new OfferNotFoundException("Не найдены предложения: " + notFoundOfferIds, null, null, 404);
        }

        return offers;
    }

    /**
     * Поиск предложений товаров по списку идентификаторов
     * @param proposedOfferIds идентификаторы предложений
     * @return список сущностей ProposedOffer
     */
    private List<ProposedOffer> findProposedOffers(List<Long> proposedOfferIds) {
        if (CollectionUtils.isEmpty(proposedOfferIds)) {
            return Collections.emptyList();
        }

        List<ProposedOffer> proposedOffers = proposedOfferRepository.findAllById(proposedOfferIds);

        Set<Long> foundIds = proposedOffers.stream()
            .map(ProposedOffer::getId)
            .collect(Collectors.toSet());

        List<Long> notFoundIds = proposedOfferIds.stream()
            .filter(id -> !foundIds.contains(id))
            .toList();

        if (!notFoundIds.isEmpty()) {
            throw new OfferNotFoundException("Не найдены предложения: " + notFoundIds, null, null, 404);
        }

        return proposedOffers;
    }

    /**
     * Проверить принадлежность предложений заявке
     * @param offers список предложений
     * @param orderId текущая заявка
     */
    private void validateOffersBelongToOrder(List<Offer> offers, Long orderId) {
        List<Long> orderIds = offerRepository.findOrderIdsByOffers(offers);

        List<Long> otherOrderIds = orderIds
            .stream()
            .filter(id -> !Objects.equals(id, orderId))
            .toList();

        if (!otherOrderIds.isEmpty()) {
            throw new ValidationException(
                "Переданы предложения от других заявок: " + otherOrderIds,
                Collections.emptyMap(), null,
                orderId, HttpStatus.BAD_REQUEST.value()
            );
        }
    }

    /**
     * Проверить принадлежность предложений от байера заявке
     * @param offers список предложений
     * @param orderId текущая заявка
     */
    private void validateProposedOffersBelongToOrder(List<ProposedOffer> offers, Long orderId) {
        List<Long> orderIds = offerRepository.findOrderIdsByProposedOffers(offers);

        List<Long> otherOrderIds = orderIds
            .stream()
            .filter(id -> !Objects.equals(id, orderId))
            .toList();

        if (!otherOrderIds.isEmpty()) {
            throw new ValidationException(
                "Переданы предложения от других заявок: " + otherOrderIds,
                Collections.emptyMap(), null,
                orderId, HttpStatus.BAD_REQUEST.value()
            );
        }
    }

    private DealUpdateRequest buildUpdateDealRequest(Long dealId, Long companyId) {
        return DealUpdateRequest.builder()
                .id(dealId)
                .fields(DealFields.builder().companyId(companyId).build())
                .build();
    }

    private void syncDealStatusOnBuyerOfferAdded(PurchaseOrder order) {
        BitrixDealAdditionalDto additionalData = BitrixDealAdditionalDto.builder()
                .dealStatus(DealStatus.EXECUTING)
                .build();

        synchronizer.exportOrder(order.getId(), additionalData);
    }

    @Override
    public Set<ProposedOfferDTO> getProposedByOfferId(Long offerId) {
        return offerRepository.findById(offerId)
                .map(p -> proposedOfferMapper.toProposedOfferDTOSet(p.getProposedOffers()))
                .orElseThrow(() -> new ProposedOfferNotFoundException("Предложение с id " + offerId + " не найдено.", null, null, 404));
    }

    @Override
    @Transactional(readOnly = true)
    public OfferDetailsDTO getOfferDetails(Long offerId) {
        if (offerId == null) {
            throw new IllegalArgumentException("Идентификатор оффера не может быть null.");
        }

        Offer offer = offerRepository.findByIdWithProposedOffers(offerId)
                .orElseThrow(() -> new ProposedOfferNotFoundException(
                        String.format("Оффер с id %d не найден.", offerId), 404));

        return offerDetailsMapper.toOfferDetailsDTO(offer);
    }
}
