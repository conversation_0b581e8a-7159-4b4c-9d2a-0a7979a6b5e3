package ru.oskelly.concierge.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import ru.oskelly.concierge.data.model.Offer;
import ru.oskelly.concierge.data.model.ProposedOffer;
import ru.oskelly.concierge.data.repository.OfferRepository;
import ru.oskelly.concierge.data.repository.ProposedOfferRepository;

import java.util.Collections;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class DefaultOfferStatusService implements OfferStatusService {

    private final OfferRepository offerRepository;
    private final ProposedOfferRepository proposedOfferRepository;

    @Override
    @Transactional
    public void setOffersAsSentToCustomer(List<Long> offerIds, List<Long> proposedOfferIds) {
        List<Offer> offers = CollectionUtils.isEmpty(offerIds)
                ? Collections.emptyList()
                : offerRepository.findAllById(offerIds);

        offers.forEach(offer -> offer.setIsSentToCustomer(true));
        offerRepository.saveAll(offers);

        List<ProposedOffer> proposedOffers = CollectionUtils.isEmpty(proposedOfferIds)
                ? Collections.emptyList()
                : proposedOfferRepository.findAllById(proposedOfferIds);

        proposedOffers.forEach(proposedOffer -> proposedOffer.setIsSentToCustomer(true));
        proposedOfferRepository.saveAll(proposedOffers);
    }
}
