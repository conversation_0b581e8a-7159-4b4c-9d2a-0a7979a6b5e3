package ru.oskelly.concierge.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.stereotype.Service;
import ru.oskelly.concierge.data.model.enums.PurchaseOrderStatusEnum;
import ru.oskelly.concierge.service.component.DefaultTransitionFinder;
import ru.oskelly.concierge.statemachine.events.PurchaseOrderTransitionEvent;

import javax.annotation.Nullable;

/**
 * Сервис для обработки переходов состояний заявок на покупку
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PurchaseOrderTransitionService {
    private final ConciergeStateMachineService conciergeStateMachineService;

    /**
     * Находит подходящее событие перехода для перемещения из текущего в целевой статус
     *
     * @param orderId       идентификатор заявки
     * @param currentStatus текущий статус заявки
     * @param targetStatus  целевой статус заявки
     * @return событие перехода
     * @throws IllegalArgumentException если найдены конфликты статусов
     */
    public PurchaseOrderTransitionEvent findTransitionEvent(long orderId,
                                                            @Nullable PurchaseOrderStatusEnum currentStatus,
                                                            @Nullable PurchaseOrderStatusEnum targetStatus) throws IllegalArgumentException {
        if (currentStatus == null || targetStatus == null) {
            throw new IllegalArgumentException("Текущий и целевой статусы не могут быть null");
        }

        if (currentStatus.equals(targetStatus)) {
            throw new IllegalArgumentException("Текущий и целевой статусы одинаковы");
        }

        val stateMachine = conciergeStateMachineService.getStateMachine(orderId);
        val transitionFinder = new DefaultTransitionFinder<>(stateMachine);

        return transitionFinder.findEvent(currentStatus, targetStatus);
    }
}