package ru.oskelly.concierge.service;

import org.springframework.stereotype.Component;
import ru.oskelly.concierge.bitrix.dto.DealStatus;
import ru.oskelly.concierge.data.model.enums.PurchaseOrderStatusEnum;

import java.util.Map;

import static java.util.Map.entry;

/**
 * Маппер для преобразования статусов заказа между PurchaseOrderStatusEnum и DealStatus.
 * Обеспечивает двунаправленное сопоставление статусов.
 */
@Component
public class DefaultBitrixDealStatusMapper implements BitrixDealStatusMapper {

    private static final Map<PurchaseOrderStatusEnum, DealStatus> ORDER_TO_DEAL_STATUS_MAP = Map.of(
            PurchaseOrderStatusEnum.NEW, DealStatus.NEW,
            PurchaseOrderStatusEnum.IN_PROGRESS_SALES, DealStatus.PREPARATION,
            PurchaseOrderStatusEnum.AWAITING_SOURCER, DealStatus.TRANSFERRED_TO_SOURCER,
            PurchaseOrderStatusEnum.IN_PROGRESS_SOURCER, DealStatus.PREPAYMENT_INVOICE,
            PurchaseOrderStatusEnum.AWAITING_SEND_TO_CLIENT, DealStatus.FINAL_INVOICE,
            PurchaseOrderStatusEnum.AWAITING_CLIENT_ANSWER, DealStatus.PRODUCT_CREATED,
            PurchaseOrderStatusEnum.DONE, DealStatus.PAID_ORDERS
    );

    private static final Map<DealStatus, PurchaseOrderStatusEnum> DEAL_TO_ORDER_STATUS_MAP = Map.ofEntries(
            entry(DealStatus.NEW, PurchaseOrderStatusEnum.NEW),
            entry(DealStatus.SEND_TO_CLIENTS, PurchaseOrderStatusEnum.NEW),
            entry(DealStatus.UNPROCESSED_INSTA, PurchaseOrderStatusEnum.NEW),
            entry(DealStatus.PREPARATION, PurchaseOrderStatusEnum.IN_PROGRESS_SALES),
            entry(DealStatus.TRANSFERRED_TO_SOURCER, PurchaseOrderStatusEnum.AWAITING_SOURCER),
            entry(DealStatus.PREPAYMENT_INVOICE, PurchaseOrderStatusEnum.IN_PROGRESS_SOURCER),
            entry(DealStatus.FINAL_INVOICE, PurchaseOrderStatusEnum.AWAITING_SEND_TO_CLIENT),
            entry(DealStatus.PRODUCT_CREATED, PurchaseOrderStatusEnum.AWAITING_CLIENT_ANSWER),
            entry(DealStatus.PRODUCT_NOT_PAID, PurchaseOrderStatusEnum.AWAITING_CLIENT_ANSWER),
            entry(DealStatus.PRODUCT_IN_CART, PurchaseOrderStatusEnum.AWAITING_CLIENT_ANSWER),
            entry(DealStatus.CART_ERROR, PurchaseOrderStatusEnum.AWAITING_CLIENT_ANSWER),
            entry(DealStatus.PAID_ORDERS, PurchaseOrderStatusEnum.DONE),
            entry(DealStatus.ON_HOLD, PurchaseOrderStatusEnum.DONE),
            entry(DealStatus.LOSE, PurchaseOrderStatusEnum.DONE),
            entry(DealStatus.SPAM, PurchaseOrderStatusEnum.DONE),
            entry(DealStatus.NOT_TARGET_REQUEST, PurchaseOrderStatusEnum.DONE),
            entry(DealStatus.APOLOGY, PurchaseOrderStatusEnum.DONE),
            entry(DealStatus.FAILED_TO_PROCESS, PurchaseOrderStatusEnum.DONE),
            entry(DealStatus.DUPLICATE, PurchaseOrderStatusEnum.DONE),
            entry(DealStatus.FAILED_EXPERTISE, PurchaseOrderStatusEnum.DONE),
            entry(DealStatus.BUYER_DECLINED_AGREEMENT, PurchaseOrderStatusEnum.DONE),
            entry(DealStatus.OTHER_REASON, PurchaseOrderStatusEnum.DONE)
    );

    /**
     * Преобразует статус заказа в статус сделки.
     *
     * @param status статус заказа
     * @return соответствующий статус сделки
     * @throws IllegalArgumentException если статус не найден
     */
    @Override
    public DealStatus toDealStatus(PurchaseOrderStatusEnum status) {
        DealStatus dealStatus = ORDER_TO_DEAL_STATUS_MAP.get(status);
        if (dealStatus == null) {
            throw new IllegalArgumentException("Неизвестный статус заказа: " + status);
        }
        return dealStatus;
    }

    /**
     * Преобразует статус сделки в статус заказа.
     *
     * @param status статус сделки
     * @return соответствующий статус заказа
     * @throws IllegalArgumentException если статус не найден
     */
    @Override
    public PurchaseOrderStatusEnum toOrderStatus(DealStatus status) {
        PurchaseOrderStatusEnum orderStatus = DEAL_TO_ORDER_STATUS_MAP.get(status);
        if (orderStatus == null) {
            throw new IllegalArgumentException("Неизвестный статус сделки: " + status);
        }
        return orderStatus;
    }
}