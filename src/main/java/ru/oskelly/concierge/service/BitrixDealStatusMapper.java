package ru.oskelly.concierge.service;

import ru.oskelly.concierge.bitrix.dto.DealStatus;
import ru.oskelly.concierge.data.model.enums.PurchaseOrderStatusEnum;

public interface BitrixDealStatusMapper {

    /**
     * Конвертация статуса заявки в статус сделки
     * @param status статус заявки
     * @return статус сделки
     */
    DealStatus toDealStatus(PurchaseOrderStatusEnum status);

    /**
     * Конвертация статуса сделки в статус заявки
     * @param status статус сделки
     * @return статус заявки
     */
    PurchaseOrderStatusEnum toOrderStatus(DealStatus status);
}
