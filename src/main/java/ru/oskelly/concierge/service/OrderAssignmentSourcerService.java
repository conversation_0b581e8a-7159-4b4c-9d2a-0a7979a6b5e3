package ru.oskelly.concierge.service;

import jakarta.annotation.Nullable;
import ru.oskelly.concierge.controller.dto.managerdto.SourcerInfoDTO;

import java.util.List;

/**
 * Сервис распределения заявок на Сорсера
 */
public interface OrderAssignmentSourcerService {

    /**
     * Запросить свободного Сорсера для заявки
     * @param orderId идентификатор заявки
     */
    void requestAvailableSourcer(long orderId);

    /**
     * Запросить свободных Сорсеров для заявок
     * @param orderIds идентификаторы заявок
     */
    void requestAvailableSourcer(List<Long> orderIds);

    /**
     * Назначить заявку Сорсеру
     * @param orderId идентификатор заявки
     * @param sourcerInfo данные Сорсера
     */
    void assignToSourcer(long orderId, SourcerInfoDTO sourcerInfo);

    /**
     * Распределить заявки одного сотрудника (Сорсера) на другого.
     * Заявки Сорсера распределяются на указанного Админа.
     * Заявки Админа распределяются на Сорсеров из очереди.
     * @param fromSourcerId заявки от Сорсера (ID)
     * @param toSourcerInfo заявки Сорсеру (Админу)
     */
    void redistribute(long fromSourcerId, @Nullable SourcerInfoDTO toSourcerInfo);
}
