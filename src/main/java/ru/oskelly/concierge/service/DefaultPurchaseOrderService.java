package ru.oskelly.concierge.service;

import jakarta.persistence.EntityNotFoundException;
import jakarta.transaction.Transactional;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.statemachine.service.StateMachineService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ru.oskelly.concierge.bitrix.dto.BitrixBaseRequest;
import ru.oskelly.concierge.bitrix.dto.BitrixContactDTO;
import ru.oskelly.concierge.bitrix.dto.BitrixFields;
import ru.oskelly.concierge.bitrix.dto.User;
import ru.oskelly.concierge.common.ContextConstants;
import ru.oskelly.concierge.common.ThreadLocalContext;
import ru.oskelly.concierge.controller.dto.ImageDTO;
import ru.oskelly.concierge.controller.dto.OfferDTO;
import ru.oskelly.concierge.controller.dto.OrderSources;
import ru.oskelly.concierge.controller.dto.PaginatedResult;
import ru.oskelly.concierge.controller.dto.PurchaseOrderCreateRequest;
import ru.oskelly.concierge.controller.dto.PurchaseOrderFilter;
import ru.oskelly.concierge.controller.dto.PurchaseOrderFullDTO;
import ru.oskelly.concierge.controller.dto.PurchaseOrderUpdateRequest;
import ru.oskelly.concierge.controller.dto.PurchaseOrderWithShipmentsCreateRequest;
import ru.oskelly.concierge.controller.dto.RequestsFilter;
import ru.oskelly.concierge.controller.dto.SalesInfoDTO;
import ru.oskelly.concierge.controller.dto.ShimpentSizeDTO;
import ru.oskelly.concierge.controller.dto.ShipmentRequestDTO;
import ru.oskelly.concierge.controller.dto.ShipmentResponseDTO;
import ru.oskelly.concierge.controller.dto.SortingOption;
import ru.oskelly.concierge.controller.dto.SortingOptionsDto;
import ru.oskelly.concierge.controller.dto.managerdto.CustomerInfoDTO;
import ru.oskelly.concierge.controller.dto.managerdto.SourcerInfoDTO;
import ru.oskelly.concierge.data.mapper.PurchaseOrderMapper;
import ru.oskelly.concierge.data.mapper.ShipmentMapper;
import ru.oskelly.concierge.data.model.PurchaseOrder;
import ru.oskelly.concierge.data.model.Shipment;
import ru.oskelly.concierge.data.model.enums.PurchaseOrderSourceEnum;
import ru.oskelly.concierge.data.model.enums.PurchaseOrderStatusEnum;
import ru.oskelly.concierge.data.model.enums.Roles;
import ru.oskelly.concierge.data.model.enums.TypesSorting;
import ru.oskelly.concierge.data.repository.PersonalShopperRepository;
import ru.oskelly.concierge.data.repository.PurchaseOrderRepository;
import ru.oskelly.concierge.exception.PurchaseOrderNotFoundException;
import ru.oskelly.concierge.exception.ValidationException;
import ru.oskelly.concierge.service.component.BestOfferComponent;
import ru.oskelly.concierge.service.component.PurchaseOrderComponent;
import ru.oskelly.concierge.service.dto.BestOfferDTO;
import ru.oskelly.concierge.service.dto.ComparisonCriterion;
import ru.oskelly.concierge.service.imageDecoder.ResourceBase64Encoder;
import ru.oskelly.concierge.statemachine.events.PurchaseOrderTransitionEvent;

import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

import static java.util.Optional.ofNullable;
import static org.apache.commons.lang3.ObjectUtils.isNotEmpty;
import static org.springframework.util.CollectionUtils.isEmpty;

@Slf4j
@Service
@RequiredArgsConstructor
public class DefaultPurchaseOrderService implements PurchaseOrderService {
    private static final String ERROR_MESSAGE = "Заявка с ID %s не найдена";
    private static final String MERCO_APP_SOURCE = "Merco: App";

    private final ConciergeStateMachineService conciergeStateMachineService;
    private final PurchaseOrderRepository purchaseOrderRepository;
    private final PurchaseOrderMapper purchaseOrderMapper;
    private final StateMachineService<PurchaseOrderStatusEnum, PurchaseOrderTransitionEvent> stateMachineService;
    private final PurchaseOrderComponent purchaseOrderComponent;
    private final ShipmentMapper shipmentMapper;
    private final StateTransitionHistoryService stateTransitionHistoryService;
    private final BestOfferComponent bestOfferComponent;
    private final BitrixService bitrixService;
    private final ResourceBase64Encoder resourceBase64Encoder;
    private final PersonalShopperRepository personalShopperRepository;

    @Override
    @Transactional
    public PurchaseOrderFullDTO createPurchaseOrder(PurchaseOrderCreateRequest request) {
        checkRequest(request);
        SalesInfoDTO salesInfoDTO = request.salesInfo();
        CustomerInfoDTO customerInfo = request.customerInfo();
        PurchaseOrder purchaseOrder = PurchaseOrder.builder()
                .customerId(customerInfo != null ? customerInfo.customerId() : null)
                .customerNickName(customerInfo != null ? customerInfo.customerNickName() : null)
                .customerPhone(customerInfo != null ? customerInfo.customerPhone() : null)
                .salesId(salesInfoDTO != null ? salesInfoDTO.salesId() : null)
                .salesRole(salesInfoDTO != null ? salesInfoDTO.salesRole() : null)
                .salesNickName(salesInfoDTO != null ? salesInfoDTO.nickName() : null)
                .salesFio(salesInfoDTO != null ? salesInfoDTO.fio() : null)
                .source(request.source())
                .description(request.description())
                .changeDate(ZonedDateTime.now())
                .status(request.purchaseToNew() ? PurchaseOrderStatusEnum.NEW : PurchaseOrderStatusEnum.DRAFT)
                .images(imageUrlsToDTO(request.imagesUrl()))
                .link(request.link())
                .bitrixDealId(request.bitrixDealId())
                .build();

        purchaseOrderRepository.save(purchaseOrder);

        log.info(
            "Создан заказ на покупку с идентификатором: {}, statusId: {}",
            purchaseOrder.getId(),
            purchaseOrder.getStatus()
        );

        conciergeStateMachineService.startStateMachine(
            purchaseOrder.getId(),
            Map.of(
                "isNew", request.purchaseToNew(),
                "orderId", purchaseOrder.getId()
            )
        );

        return purchaseOrderMapper.toFullDto(purchaseOrder, personalShopperRepository);
    }

    @Override
    @Transactional
    public PurchaseOrderFullDTO createPurchaseOrderWithShipments(PurchaseOrderWithShipmentsCreateRequest request) {
        SalesInfoDTO salesInfoDTO = request.salesInfo();
        CustomerInfoDTO customerInfo = request.customerInfo();

        PurchaseOrder purchaseOrder = PurchaseOrder.builder()
                .customerId(customerInfo != null ? customerInfo.customerId() : null)
                .customerNickName(customerInfo != null ? customerInfo.customerNickName() : null)
                .customerPhone(customerInfo != null ? customerInfo.customerPhone() : null)
                .salesId(salesInfoDTO != null ? salesInfoDTO.salesId() : null)
                .salesRole(salesInfoDTO != null ? salesInfoDTO.salesRole() : null)
                .salesNickName(salesInfoDTO != null ? salesInfoDTO.nickName() : null)
                .salesFio(salesInfoDTO != null ? salesInfoDTO.fio() : null)
                .source(request.source())
                .description(request.description())
                .changeDate(ZonedDateTime.now())
                .status(request.initialStatus())
                .images(imageUrlsToDTO(request.imagesUrl()))
                .build();

        Set<Shipment> shipmentEntities = shipmentMapper.toEntityUpdateSet(request.shipments(), purchaseOrder);
        purchaseOrder.setShipments(shipmentEntities);

        purchaseOrderRepository.save(purchaseOrder);

        log.info(
            "Создан заказ на покупку со списком товаров с идентификатором: {}, statusId: {}",
            purchaseOrder.getId(),
            purchaseOrder.getStatus()
        );

        boolean isNewStatus = request.initialStatus().equals(PurchaseOrderStatusEnum.NEW);
        boolean isAwaitingSourcerStatus = request.initialStatus().equals(PurchaseOrderStatusEnum.AWAITING_SOURCER);

        conciergeStateMachineService.startStateMachine(
            purchaseOrder.getId(),
            Map.of(
                "isNew", isNewStatus,
                "isAwaitingSourcer", isAwaitingSourcerStatus,
                "orderId", purchaseOrder.getId()
            )
        );

        purchaseOrder.setBitrixDealId(bitrixService.createDeal(
                createDealAddRequest(request, purchaseOrder.getId(), purchaseOrder.getCreationDate())
        ).getResult());
        purchaseOrderRepository.save(purchaseOrder);

        return purchaseOrderMapper.toFullDto(purchaseOrder, personalShopperRepository);
    }

    private void checkRequest(PurchaseOrderCreateRequest request) {
        boolean hasDescription = request.description() != null && !request.description().trim().isEmpty();
        boolean hasImages = request.imagesUrl() != null && !request.imagesUrl().isEmpty();

        if (!hasDescription && !hasImages) {
            throw new ValidationException("Заявка должна содержать описание или изображения", null, null, null, 400);
        }
    }

    /**
     * Обработка события для stateMachine заявки PurchaseOrder.
     * Запускает обработку события в stateMachine и обновляет статус PurchaseOrder в бд.
     *
     * @param orderId - id заявки
     * @param event   - обрабатываемое событие
     * @param role    - роль пользователя, выполняющего запрос
     * @return обновленный PurchaseOrderFullDTO
     */
    @Override
    @Transactional
    public PurchaseOrderFullDTO eventProcessing(Long orderId, PurchaseOrderTransitionEvent event, Roles role) {
        log.info("Обработка события {} с ролью пользователя {} для заявки с ID {}", event, role, orderId);
        final PurchaseOrder purchaseOrder = purchaseOrderRepository.findById(orderId)
                .orElseThrow(() -> new PurchaseOrderNotFoundException(String.format(ERROR_MESSAGE, orderId), null, orderId, 404));

        val stateMachine = conciergeStateMachineService.getStateMachine(orderId);

        ofNullable(ThreadLocalContext.get(ContextConstants.USER_ID, Long.class))
            .ifPresent(u -> stateMachine.getExtendedState()
                .getVariables()
                .put(ContextConstants.USER_ID, u));
        ofNullable(role)
            .ifPresent(r -> stateMachine.getExtendedState()
                .getVariables()
                .put(ContextConstants.ROLE, r));

        if (PurchaseOrderTransitionEvent.SALES_WORK_START.equals(event)
                && purchaseOrder.getSalesId() != null) {

            stateMachine.getExtendedState()
                .getVariables()
                .put(ContextConstants.SALES_ID.name(), purchaseOrder.getSalesId());
        }

        if (PurchaseOrderTransitionEvent.SOURCER_WORK_START.equals(event)
                && purchaseOrder.getSourcerId() != null) {

            stateMachine.getExtendedState()
                .getVariables()
                .put(ContextConstants.SOURCER_ID.name(), purchaseOrder.getSourcerId());
        }

        conciergeStateMachineService.sendEvent(stateMachine, event);

        purchaseOrder.setStatus(stateMachine.getState().getId());
        purchaseOrder.setChangeDate(ZonedDateTime.now());
        purchaseOrderRepository.save(purchaseOrder);

        stateMachineService.releaseStateMachine(orderId.toString());
        return purchaseOrderMapper.toFullDto(purchaseOrder, personalShopperRepository);
    }

    @Override
    public PurchaseOrderFullDTO getPurchaseOrder(Long orderId, Roles role, Long customerId) {
        boolean isOffersVisible = isOffersVisibleForRole(role, orderId);
        var purchaseOrderEntity = getPurchaseOrderEntity(orderId, customerId, isOffersVisible);

        if (!isOffersVisible) {
            return purchaseOrderMapper.toFullDtoWithoutOffers(purchaseOrderEntity);
        }

        PurchaseOrderFullDTO fullDto = purchaseOrderMapper.toFullDto(purchaseOrderEntity, personalShopperRepository);
        fullDto.shipments().forEach(this::enrichShipmentWithBestOffers);

        return fullDto;
    }

    @Transactional
    @Override
    public void updateChatId(Long orderId, Long chatId) {
        purchaseOrderRepository.findById(orderId).ifPresent(order -> {
            order.setChatId(chatId);
            purchaseOrderRepository.save(order);
        });
    }

    private boolean isOffersVisibleForRole(Roles role, Long orderId) {
        return role == Roles.SOURCER
                || role == Roles.CONCIERGE_SOURCERS_ADMIN
                || stateTransitionHistoryService.checkStatusPassed(orderId, PurchaseOrderStatusEnum.AWAITING_SEND_TO_CLIENT);
    }

    private void enrichShipmentWithBestOffers(ShipmentResponseDTO shipmentResponseDTO) {
        List<BestOfferDTO> bestOffers = bestOfferComponent.findBestOffers(shipmentResponseDTO.id());
        if (bestOffers.isEmpty()) return;

        Map<Long, Set<ComparisonCriterion>> offerCriteriaMap = bestOffers.stream()
                .filter(bestOffer -> bestOffer.getOffer() != null)
                .collect(Collectors.toMap(
                        bestOffer -> bestOffer.getOffer().getId(),
                        BestOfferDTO::getCriteria,
                        (existing, replacement) -> {
                            Set<ComparisonCriterion> merged = new HashSet<>(existing);
                            merged.addAll(replacement);
                            return merged;
                        }
                ));

        List<OfferDTO> updatedOffers = shipmentResponseDTO.offers().stream()
                .map(offer -> offerCriteriaMap.containsKey(offer.id())
                        ? createUpdatedOffer(offer, offerCriteriaMap.get(offer.id()))
                        : offer)
                .toList();

        shipmentResponseDTO.offers().clear();
        shipmentResponseDTO.offers().addAll(updatedOffers);
    }

    private OfferDTO createUpdatedOffer(OfferDTO originalOffer, Set<ComparisonCriterion> criteria) {
        return new OfferDTO(
                originalOffer.id(),
                criteria,
                originalOffer.shipmentId(),
                originalOffer.seller(),
                originalOffer.shopper(),
                originalOffer.product(),
                originalOffer.type(),
                originalOffer.sellerType(),
                originalOffer.creationDate(),
                originalOffer.buyerOffers()
        );
    }

    @Override
    public PurchaseOrder getPurchaseOrderEntity(Long orderId, Long customerId) {
        if (orderId == null) {
            throw new EntityNotFoundException("ID заявки не может быть null");
        }
        if (customerId == null) {
            return purchaseOrderRepository.findById(orderId)
                    .orElseThrow(() -> new PurchaseOrderNotFoundException(String.format(ERROR_MESSAGE, orderId), null, orderId, 404));
        }
        return purchaseOrderRepository.findByIdAndCustomerId(orderId, customerId)
                .orElseThrow(() -> new PurchaseOrderNotFoundException(String.format(ERROR_MESSAGE, orderId), null, orderId, 404));
    }

    @Override
    public PurchaseOrder getPurchaseOrderEntity(Long orderId, Long customerId, boolean withOffers) {
        if (orderId == null) {
            throw new EntityNotFoundException("ID заявки не может быть null");
        }
        if (withOffers) {
            Optional<PurchaseOrder> purchaseOrder = customerId == null
                    ? purchaseOrderRepository.findWithOffersById(orderId)
                    : purchaseOrderRepository.findWithOffersByIdAndCustomerId(orderId, customerId);
            return purchaseOrder
                    .orElseThrow(() -> new PurchaseOrderNotFoundException(String.format(ERROR_MESSAGE, orderId), null, orderId, 404));
        }
        return getPurchaseOrderEntity(orderId, customerId);
    }

    @Override
    @Transactional
    public PurchaseOrderFullDTO updatePurchaseOrder(Long orderId,
                                                    Long userId,
                                                    PurchaseOrderUpdateRequest updateRequest) {

        var purchaseOrder = purchaseOrderRepository.findByIdAndCustomerId(orderId, userId)
                .orElseThrow(() -> new PurchaseOrderNotFoundException(String.format(ERROR_MESSAGE, orderId), null, orderId, 404));

        // check for DRAFT status
        if (!PurchaseOrderStatusEnum.DRAFT.equals(purchaseOrder.getStatus())) {
            throw new ValidationException("Нельзя обновить заявку в статусе DRAFT", null, null, null, 400);
        }

        purchaseOrder.setLink(updateRequest.link());
        purchaseOrder.setChangeDate(ZonedDateTime.now());
        purchaseOrder.setDescription(updateRequest.description());
        purchaseOrder.setImages(updateRequest.imagesUrl().stream()
                .map(url -> new ImageDTO(UUID.randomUUID(), url, ZonedDateTime.now())).toList());

        purchaseOrder = purchaseOrderRepository.save(purchaseOrder);

        log.info("Заявка с ID {} обновлена пользователем с ID {}", orderId, userId);
        return purchaseOrderMapper.toFullDto(purchaseOrder, personalShopperRepository);
    }

    @Override
    public PurchaseOrderFilter getPurchaseOrderStatusCountsAndStatuses(PurchaseOrderFilter filter, Roles role) {
        Long userId = ThreadLocalContext.get(ContextConstants.USER_ID, Long.class);
        var stats = purchaseOrderComponent.getPurchaseOrderStats(filter, userId, role);

        return PurchaseOrderFilter.builder()
                .groupingStatusQuantities(stats.statusStats())
                .groupingSourceQuantities(stats.sourceStats())
                .page(filter.page())
                .pageSize(filter.pageSize())
                .brands(filter.brands())
                .models(filter.models())
                .toDate(filter.toDate())
                .fromDate(filter.fromDate())
                .typesSorting(createSortingOptionsDto(filter.typesSorting()))
                .build();
    }

    private SortingOptionsDto createSortingOptionsDto(SortingOptionsDto sortingOptionsDto) {
        var defaultSortingOptions = Arrays.stream(TypesSorting.values())
                .map(type -> new SortingOption(type, type == TypesSorting.DATE)).toList();

        // Если входной DTO пустой, возвращаем дефолтный список
        if (sortingOptionsDto == null || sortingOptionsDto.sorting() == null || sortingOptionsDto.sorting().isEmpty()) {
            return new SortingOptionsDto(defaultSortingOptions);
        }

        Map<TypesSorting, Boolean> inputSelections = sortingOptionsDto.sorting().stream()
                .collect(Collectors.toMap(SortingOption::code, SortingOption::isSelected));

        List<SortingOption> updatedOptions = defaultSortingOptions.stream()
                .map(option -> {
                    boolean isSelected = inputSelections.containsKey(option.code())
                            ? inputSelections.get(option.code())
                            : option.isSelected();
                    return new SortingOption(option.code(), isSelected);
                })
                .toList();

        return new SortingOptionsDto(updatedOptions);
    }

    @Override
    public PaginatedResult getPurchaseOrders(RequestsFilter filter, String searchText, Roles role) {
        Long userId = ThreadLocalContext.get(ContextConstants.USER_ID, Long.class);
        return purchaseOrderComponent.findAllWithFilter(filter, userId, searchText, role);
    }

    @Override
    public OrderSources getSources() {
        return OrderSources.builder()
                .sources(Arrays.stream(PurchaseOrderSourceEnum.values())
                        .collect(Collectors.toMap(
                                source -> source,
                                PurchaseOrderSourceEnum::getDescription)))
                .build();
    }

    @Override
    @Transactional
    public void assignSales(Long orderId, SalesInfoDTO sales) {
        log.info("Назначение Sales {} для заявки с ID: {}", sales.salesId(), orderId);
        final PurchaseOrder purchaseOrder = purchaseOrderRepository.findById(orderId)
            .orElseThrow(() -> new PurchaseOrderNotFoundException(String.format(ERROR_MESSAGE, orderId), null, orderId, 404));

        purchaseOrder.setSalesId(sales.salesId());
        purchaseOrder.setSalesFio(sales.fio());
        purchaseOrder.setSalesRole(sales.salesRole());

        if (sales.email() != null) {
            final User user = bitrixService.findUserByEmail(sales.email());
            purchaseOrder.setBitrixSalesId(user.id());

            bitrixService.assignUserToDeal(user.id(), purchaseOrder.getBitrixDealId());
        }
        purchaseOrderRepository.save(purchaseOrder);
    }

    @Override
    @Transactional
    public void assignSales(List<Long> orderIds, SalesInfoDTO sales) {
        log.info("Назначение Sales {} для заявок: {}", sales.salesId(), orderIds);
        final List<PurchaseOrder> orders = purchaseOrderRepository.findAllById(orderIds);
        orders.forEach(purchaseOrder -> {
                purchaseOrder.setSalesId(sales.salesId());
                purchaseOrder.setSalesFio(sales.fio());
                purchaseOrder.setSalesRole(sales.salesRole());
            });

        if (sales.email() != null) {
            final User user = bitrixService.findUserByEmail(sales.email());
            for (PurchaseOrder order : orders) {
                order.setBitrixSalesId(user.id());

                bitrixService.assignUserToDeal(user.id(), order.getBitrixDealId());
            }
        }
       purchaseOrderRepository.saveAll(orders);
    }

    @Override
    @Transactional
    public void assignSourcer(Long orderId, SourcerInfoDTO sourcer) {
        log.info("Назначение Сорсера {} для заявки с ID: {}", sourcer.sourcerId(), orderId);
        final PurchaseOrder purchaseOrder = purchaseOrderRepository
            .findById(orderId)
            .orElseThrow(() -> new PurchaseOrderNotFoundException(String.format(ERROR_MESSAGE, orderId), null, orderId, 404));

        purchaseOrder.setSourcerId(sourcer.sourcerId());
        val savedOrder = purchaseOrderRepository.save(purchaseOrder);

        if (sourcer.email() != null) {
            final User user = bitrixService.findUserByEmail(sourcer.email());
            bitrixService.assignUserToDeal(user.id(), savedOrder.getBitrixDealId());
        }
    }

    @Override
    @Transactional
    public void assignSourcer(List<Long> orderIds, SourcerInfoDTO sourcer) {
        log.info("Назначение Сорсера {} для заявок: {}", sourcer.sourcerId(), orderIds);
        final List<PurchaseOrder> orders = purchaseOrderRepository.findAllById(orderIds);

        orders.forEach(purchaseOrder -> purchaseOrder.setSourcerId(sourcer.sourcerId()));

        val savedOrders = purchaseOrderRepository.saveAll(orders);

        if (sourcer.email() != null) {
            final User user = bitrixService.findUserByEmail(sourcer.email());
            savedOrders.forEach(savedOrder -> bitrixService.assignUserToDeal(user.id(), savedOrder.getBitrixDealId()));
        }
    }

    private BitrixBaseRequest createDealAddRequest(PurchaseOrderWithShipmentsCreateRequest request, Long orderId, ZonedDateTime creationTime) {
        long contactId = ensureBitrixContact(request.customerInfo().customerNickName(), request.customerInfo().customerPhone());
        BitrixBaseRequest bitrixRequest = BitrixBaseRequest.builder().build();
        List<ShipmentRequestDTO> shipments = request.shipments();
        for (int i = 0; i < shipments.size(); i++) {
            ShipmentRequestDTO shipment = shipments.get(i);
            int idx = i + 1;
            String suffix = "_" + idx;
            if (shipment.categoryName() != null) bitrixRequest.addField("UF_CRM_CATEGORY" + suffix, shipment.categoryName());
            if (shipment.brandName() != null) bitrixRequest.addField("UF_CRM_BRAND" + suffix, shipment.brandName());
            if (shipment.modelName() != null) bitrixRequest.addField("UF_CRM_MODEL" + suffix, shipment.modelName());
            if (shipment.colorAttributeName() != null) bitrixRequest.addField("UF_CRM_COLOR" + suffix, shipment.colorAttributeName());
            if (shipment.materialAttributeName() != null) bitrixRequest.addField("UF_CRM_MATERIAL" + suffix, shipment.materialAttributeName());
            if (shipment.shipmentSize() != null) bitrixRequest.addField("UF_CRM_SIZE" + suffix, buildSize(shipment.shipmentSize()));
            if (!CollectionUtils.isEmpty(shipment.links())) bitrixRequest.addField("UF_CRM_LINK" + suffix, shipment.links().getFirst());
            if (shipment.description() != null) bitrixRequest.addField("UF_CRM_DESCRIPTION" + suffix, shipment.description());
            if (shipment.description() != null) bitrixRequest.addField("UF_CRM_IMAGE" + suffix, prepareImageFiles(shipment.images()));
        }

        bitrixRequest.addField("CONTACT_ID", Long.toString(contactId));
        bitrixRequest.addField("UF_CRM_SOURCE", MERCO_APP_SOURCE);
        bitrixRequest.addField("UF_CRM_MIRKO_ID", orderId.toString());
        bitrixRequest.addField("UF_CRM_CREATION_DATE", creationTime.toString());
        return bitrixRequest;
    }

    private long ensureBitrixContact(String contactName, String contactPhone) {
        List<BitrixContactDTO> contacts = getContactsByPhone(contactPhone);
        if (isNotEmpty(contacts)) {
            return contacts.get(0).getId();
        } else {
            return createContact(contactName, contactPhone);
        }
    }

    private List<BitrixContactDTO> getContactsByPhone(String contactPhone) {
        return bitrixService.listContacts(contactPhone).getResult();
    }

    private long createContact(String name, @NonNull String phone) {

        BitrixBaseRequest request = BitrixBaseRequest.builder().build();
        request.addField(BitrixFields.NAME, name);
        request.addField(BitrixFields.OPENED, BitrixFields.YES);
        request.addField(BitrixFields.PHONE, List.of(
                        Map.of(BitrixFields.VALUE, phone, BitrixFields.VALUE_TYPE, BitrixFields.MOBILE))
        );

        return bitrixService.createContact(request).getResult();
    }

    private List<Map<String, List<String>>> prepareImageFiles(List<ImageDTO> images) {

        if (isEmpty(images)) return null;

        return images.stream()
                .map(ImageDTO::url)
                .filter(StringUtils::hasText)
                .map(this::prepareImageFile)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private Map<String, List<String>> prepareImageFile(String url) {
        try {
            String encodedFileContent = resourceBase64Encoder.getBase64StringByUrl(url);
            String[] urlSegments = url.split("/");
            String fileName = urlSegments[urlSegments.length - 1];
            return Map.of("fileData", List.of(fileName, encodedFileContent));
        } catch (Exception e) {
            // игнорируем файлы с ошибками
            log.error(e.getMessage(), e);
            return null;
        }
    }

    @Override
    @Transactional
    public void updateCustomerId(Long oldCustomerId, Long newCustomerId) {
        List<PurchaseOrder> orders = purchaseOrderRepository.findByCustomerId(oldCustomerId);

        if (orders.isEmpty()) {
            log.warn("Не найдены заявки для клиента: {}", oldCustomerId);
            return;
        }

        orders.forEach(order -> order.setCustomerId(newCustomerId));

        purchaseOrderRepository.saveAll(orders);
    }

    private List<ImageDTO> imageUrlsToDTO(List<String> urls) {
        return CollectionUtils.isEmpty(urls)
            ? new ArrayList<>()
            : urls
                .stream()
                .filter(Objects::nonNull)
                .map(url -> new ImageDTO(UUID.randomUUID(), url, ZonedDateTime.now()))
                .toList();
    }

    private String buildSize(ShimpentSizeDTO shipmentSize) {
        return "%s %s"
                .formatted(shipmentSize.type(), shipmentSize.sizeId());
    }

}
