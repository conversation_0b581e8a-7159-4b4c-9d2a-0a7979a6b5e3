package ru.oskelly.concierge.service;

import ru.oskelly.concierge.service.dto.BitrixDealAdditionalDto;

public interface PurchaseOrderBitrixSynchronizer {
    /**
     * Синхронизация заявки со сделкой в Bitrix
     *
     * @param orderId          идентификатор заявки
     * @param forcedUpdateData данные для принудительного обновления сделки
     */
    void exportOrder(Long orderId, BitrixDealAdditionalDto forcedUpdateData);

    /**
     * Экспорт всех заявок в Bitrix
     */
    void exportOrders();

    /**
     * Синхронизация сделки Bitrix с заявкой
     *
     * @param dealId идентификатор сделки в Bitrix
     */
    void importOrder(Long dealId);
}
