package ru.oskelly.concierge.exception;

/**
 * Исключение, выбрасываемое при сбое операций синхронизации с Bitrix
 */
public class BitrixSynchronizationException extends RuntimeException {
    private final Long orderId;
    private final Long dealId;

    public BitrixSynchronizationException(String message, Long orderId, Long dealId, Throwable cause) {
        super(message, cause);
        this.orderId = orderId;
        this.dealId = dealId;
    }

    public Long getOrderId() {
        return orderId;
    }

    public Long getDealId() {
        return dealId;
    }
}