package ru.oskelly.concierge.common;

import lombok.Getter;

/**
 * Класс для хранения констант контекста
 */
@Getter
public enum ContextConstants {
    USER_ID("Идентификатор пользователя"),
    STATE_MACHINE_ID("Идентификатор машины состояний"),
    STATE_MACHINE_ERROR("Ошибка машины состояний"),
    STATE_MACHINE_ERROR_MESSAGE("Сообщение об ошибке машины состояний"),
    REASON_RETURN("Причина возврата"),
    ROLE("Роль пользователя"),
    SALES_ID("Идентификатор сейлза"),
    SOURCER_ID("Идентификатор сорсера"),
    COMMENT("Комментарий");

    private final String value;

    ContextConstants(String value) {
        this.value = value;
    }
}
