package ru.oskelly.concierge.bitrix.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;

@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
public record DealFields(
        @JsonProperty("COMPANY_ID")
        Long companyId,

        @JsonProperty("STAGE_ID")
        String stageId,

        @JsonProperty("ASSIGNED_BY_ID")
        Long assignedById
) {
}