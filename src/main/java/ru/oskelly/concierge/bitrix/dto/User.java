package ru.oskelly.concierge.bitrix.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * Данные пользователя Bitrix
 *
 * @param id         ID пользователя
 * @param name       Имя
 * @param lastName   Фамилия
 * @param secondName Отчество
 * @param email      Email пользователя
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public record User(
    @JsonProperty("ID")
    Long id,

    @JsonProperty("NAME")
    String name,

    @JsonProperty("LAST_NAME")
    String lastName,

    @JsonProperty("SECOND_NAME")
    String secondName,

    @JsonProperty("EMAIL")
    String email
) {
}