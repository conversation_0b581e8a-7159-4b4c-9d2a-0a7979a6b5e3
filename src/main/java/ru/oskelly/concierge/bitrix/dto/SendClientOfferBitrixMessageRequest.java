package ru.oskelly.concierge.bitrix.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * Сообщение-запрос на отправку предложений клиенту в открытые линии
 *
 * @param chatId id чата в открытых линиях
 * @param text   текст сообщения с предложениями
 */
public record SendClientOfferBitrixMessageRequest(
        @JsonProperty("CHAT_ID")
        @NotNull(message = "CHAT_ID не должен быть null")
        Long chatId,

        @JsonProperty("MESSAGE")
        @NotBlank(message = "MESSAGE не должна быть пустой")
        String text
) {
}
