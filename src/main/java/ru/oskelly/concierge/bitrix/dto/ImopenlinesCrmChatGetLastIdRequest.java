package ru.oskelly.concierge.bitrix.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * DTO на получение id чата с клиентом в открытых линииях
 *
 * @param crmEntityType тип сущности
 * @param crmEntity     id сущности
 */
public record ImopenlinesCrmChatGetLastIdRequest(
        @JsonProperty("CRM_ENTITY_TYPE") String crmEntityType,
        @JsonProperty("CRM_ENTITY") Long crmEntity
) {
}