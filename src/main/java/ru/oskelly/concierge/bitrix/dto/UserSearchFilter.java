package ru.oskelly.concierge.bitrix.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;

/**
 * Фильтр для поиска пользователей Bitrix
 *
 * @param email          Email пользователя
 * @param name           Имя
 * @param lastName       Фамилия
 * @param secondName     Отчество
 * @param personalPhone  Личный телефон
 * @param personalMobile Личный мобильный
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@Builder
public record UserSearchFilter(
    @JsonProperty("EMAIL")
    String email,

    @JsonProperty("NAME")
    String name,

    @JsonProperty("LAST_NAME")
    String lastName,

    @JsonProperty("SECOND_NAME")
    String secondName,

    @JsonProperty("PERSONAL_PHONE")
    String personalPhone,

    @JsonProperty("PERSONAL_MOBILE")
    String personalMobile
) {
}