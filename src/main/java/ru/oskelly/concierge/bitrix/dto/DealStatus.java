package ru.oskelly.concierge.bitrix.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Енам соответствия статуса сделки Bitrix.
 */
@Getter
@AllArgsConstructor
public enum DealStatus {
    NEW("NEW", "Необработанные"),
    SEND_TO_CLIENTS("UC_10Q1L2", "Рассылка клиентам"),
    UNPROCESSED_INSTA("UC_YW90A9", "Необработанные (Insta)"),
    PREPARATION("PREPARATION", "В работе"),
    TRANSFERRED_TO_SOURCER("UC_H7FB2J", "Передана сорсеру"),
    PREPAYMENT_INVOICE("PREPAYMENT_INVOICE", "Передана байеру"),
    EXECUTING("EXECUTING", "Получено предложение от байера"),
    FINAL_INVOICE("FINAL_INVOICE", "Предложение отправлено заказчику"),
    PRODUCT_CREATED("UC_A3QMTN", "Товар создан"),
    PRODUCT_NOT_PAID("UC_Z0YXC3", "Товар не оплачен"),
    PRODUCT_IN_CART("UC_TBWS0Z", "Товар в корзине"),
    CART_ERROR("UC_BZBD1X", "Ошибка с корзиной"),
    PAID_ORDERS("UC_9UNSN7", "Оплаченные заказы"),
    BUYER_ACCEPTED("UC_323KYA", "Байер принял заказ"),
    PURCHASED_WAITING_SHIPMENT("UC_9LO3MA", "Закуплен, ожидаем отправления"),
    WAITING_DELIVERY("UC_V1P49A", "Ожидаем доставки"),
    IN_EXPERTISE("UC_ZGFVFC", "На экспертизе"),
    NONCONFORMITY_FOUND("UC_1MEPDK", "Обнаружены несоответствия"),
    TRANSFERRED_TO_LOGISTICS("UC_SU9BWM", "Передано в отдел логистики"),
    CLIENT_RECEIVED("UC_LY9VDA", "Клиент получил заказ"),
    WON("WON", "Сделка успешна"),
    ON_HOLD("UC_030GMA", "На паузе"),
    LOSE("LOSE", "Заказчик отменил заявку"),
    SPAM("UC_HOHRMQ", "Спам"),
    NOT_TARGET_REQUEST("UC_IXI6LH", "Нецелевое обращение"),
    APOLOGY("APOLOGY", "Неверные контактные данные"),
    FAILED_TO_PROCESS("1", "Не смогли выполнить заявку"),
    DUPLICATE("3", "Дубль"),
    FAILED_EXPERTISE("4", "Не прошел экспертизу"),
    BUYER_DECLINED_AGREEMENT("6", "Байер отказался от договоренностей"),
    OTHER_REASON("7", "Другая причина");

    private final String code;
    private final String description;

    private static final Map<String, DealStatus> CODE_MAP = Stream.of(values())
        .collect(Collectors.toUnmodifiableMap(DealStatus::getCode, status -> status));

    /**
     * Возвращает элемент DealStatus по коду.
     *
     * @param code код статуса
     * @return DealStatus соответствующий коду
     * @throws IllegalArgumentException если код не найден
     */
    public static DealStatus of(String code) throws IllegalArgumentException {
        final DealStatus status = CODE_MAP.get(code);
        if (status == null) {
            throw new IllegalArgumentException("Неизвестный код статуса сделки: " + code);
        }
        return status;
    }
}
