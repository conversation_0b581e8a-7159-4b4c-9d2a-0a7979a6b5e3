package ru.oskelly.concierge.bitrix.client;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import ru.oskelly.concierge.bitrix.config.BitrixFeignConfig;
import ru.oskelly.concierge.bitrix.dto.BitrixBaseRequest;
import ru.oskelly.concierge.bitrix.dto.BitrixBaseResponse;
import ru.oskelly.concierge.bitrix.dto.BitrixContactDTO;
import ru.oskelly.concierge.bitrix.dto.DealGetRequest;
import ru.oskelly.concierge.bitrix.dto.DealResponse;
import ru.oskelly.concierge.bitrix.dto.DealUpdateRequest;
import ru.oskelly.concierge.bitrix.dto.OfferCreateRequest;
import ru.oskelly.concierge.bitrix.dto.ShopperListRequest;
import ru.oskelly.concierge.bitrix.dto.ShopperResponse;
import ru.oskelly.concierge.bitrix.dto.User;
import ru.oskelly.concierge.bitrix.dto.UserFieldsResponse;
import ru.oskelly.concierge.bitrix.dto.UserSearchFilter;

import java.util.List;

@FeignClient(
    name = "bitrixClient",
    url = "${bitrix.url}",
    configuration = BitrixFeignConfig.class
)
public interface BitrixClient {
    /**
     * Получение списка существующих шопперов в Bitrix (через метод {@code crm.company.list}).
     *
     * @param request объект {@link ShopperListRequest}, содержащий список полей для выборки у шопперов.
     * @return {@link BitrixBaseResponse} с результатом операции в виде списка объектов шопперов {@link ShopperResponse}.
     */
    @PostMapping(
            value = "crm.company.list",
            consumes = MediaType.APPLICATION_JSON_VALUE,
            produces = MediaType.APPLICATION_JSON_VALUE
    )
    BitrixBaseResponse<List<ShopperResponse>> getShoppers(@RequestBody ShopperListRequest request);


    /**
     * Обновление полей существующей сделки в Bitrix (через метод {@code crm.quote.add}).
     *
     * @param request объект {@link DealUpdateRequest}, содержащий идентификатор сделки и набор полей для обновления.
     * @return {@link BitrixBaseResponse} с результатом операции в виде строки:
     *         "true" при успешном обновлении, "false" — в противном случае.
     */
    @PostMapping(
            value = "crm.deal.update",
            consumes = MediaType.APPLICATION_JSON_VALUE,
            produces = MediaType.APPLICATION_JSON_VALUE
    )
    BitrixBaseResponse<String> updateDeal(@RequestBody DealUpdateRequest request);

    /**
     * Получение сделки
     *
     * @param request идентификатор сделки
     * @return сделка
     */
    @PostMapping(
        value = "crm.deal.get",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE
    )
    BitrixBaseResponse<DealResponse> getDeal(@RequestBody DealGetRequest request);

    /**
     * Создание нового коммерческого предложения (quote) для шоппера в Bitrix (через метод {@code crm.quote.add}).
     *
     * @param request объект {@link OfferCreateRequest}, содержащий данные
     *                для формирования предложения (ID сделки, сумма, товары и пр.).
     * @return {@link BitrixBaseResponse} с целочисленным идентификатором созданного предложения.
     */
    @PostMapping(
            value = "crm.quote.add",
            consumes = MediaType.APPLICATION_JSON_VALUE,
            produces = MediaType.APPLICATION_JSON_VALUE
    )
    BitrixBaseResponse<Integer> createOffer(@RequestBody OfferCreateRequest request);

    /**
     * Получение пользовательских полей в Bitrix (через метод {@code user.fields}).
     *
     * @return {@link BitrixBaseResponse} с результатом операции в виде объекта с пользовательскими полями {@link UserFieldsResponse}.
     */
    @PostMapping(
        value = "user.fields",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE
    )
    BitrixBaseResponse<UserFieldsResponse> getUserFields();

    /**
     * Пример: выполняет action Bitrix с именем "crm.deal.add"
     */
    @PostMapping(
            value = "crm.deal.add",
            consumes = MediaType.APPLICATION_JSON_VALUE,
            produces = MediaType.APPLICATION_JSON_VALUE
    )
    BitrixBaseResponse<Long> createDeal(@RequestBody BitrixBaseRequest request);

    @PostMapping(
            value = "crm.contact.list",
            consumes = MediaType.APPLICATION_JSON_VALUE,
            produces = MediaType.APPLICATION_JSON_VALUE
    )
    BitrixBaseResponse<List<BitrixContactDTO>> listContacts(@RequestBody BitrixBaseRequest request);


    @PostMapping(
            value = "crm.contact.add",
            consumes = MediaType.APPLICATION_JSON_VALUE,
            produces = MediaType.APPLICATION_JSON_VALUE
    )
    BitrixBaseResponse<Long> createContact(@RequestBody BitrixBaseRequest request);

    /**
     * Поиск пользователей по фильтру
     * @param filter фильтр поиска
     * @return список пользователей
     */
    @PostMapping(
        value = "user.get",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE
    )
    BitrixBaseResponse<List<User>> findUsers(UserSearchFilter filter);
}
