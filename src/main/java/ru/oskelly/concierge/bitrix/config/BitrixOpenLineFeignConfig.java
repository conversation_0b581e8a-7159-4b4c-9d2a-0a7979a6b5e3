package ru.oskelly.concierge.bitrix.config;

import feign.Logger;
import feign.codec.ErrorDecoder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import ru.oskelly.concierge.bitrix.config.interceptor.BitrixOpenLineRequestInterceptor;

@Configuration
public class BitrixOpenLineFeignConfig {

    @Bean
    public ErrorDecoder bitrixOpenLineFeignErrorDecoder() {
        return new BitrixErrorDecoder();
    }

    @Bean
    public BitrixOpenLineRequestInterceptor bitrixOpenLineRequestInterceptor() {
        return new BitrixOpenLineRequestInterceptor();
    }

    @Bean
    Logger.Level BitrixOpenLineFeignLoggerLevel() {
        return Logger.Level.FULL;
    }
}
