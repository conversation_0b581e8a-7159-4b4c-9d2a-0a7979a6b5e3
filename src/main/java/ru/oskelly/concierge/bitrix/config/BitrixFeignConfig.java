package ru.oskelly.concierge.bitrix.config;

import feign.Logger;
import feign.Request;
import feign.RequestInterceptor;
import feign.codec.ErrorDecoder;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import ru.oskelly.concierge.bitrix.config.interceptor.BitrixRequestInterceptor;
import ru.oskelly.concierge.config.BitrixSynchronizerProperties;

@Configuration
@RequiredArgsConstructor
public class BitrixFeignConfig {

    private final BitrixSynchronizerProperties properties;

    @Bean
    public ErrorDecoder bitrixFeignErrorDecoder() {
        return new BitrixErrorDecoder();
    }

    @Bean
    public RequestInterceptor bitrixRequestInterceptor() {
        return new BitrixRequestInterceptor();
    }

    @Bean
    public Request.Options bitrixRequestOptions() {
        long timeoutMillis = properties.getRequestTimeout().toMillis();
        return new Request.Options(
            (int) timeoutMillis, // connectTimeout
            (int) timeoutMillis  // readTimeout
        );
    }

    @Bean
    Logger.Level BitrixFeignLoggerLevel() {
        return Logger.Level.FULL;
    }
}
