package ru.oskelly.concierge.bitrix.config;

import feign.Response;
import feign.codec.ErrorDecoder;
import lombok.extern.slf4j.Slf4j;
import ru.oskelly.concierge.exception.BitrixClientException;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

/**
 * Декодер ошибок для Bitrix API.
 */
@Slf4j
public class BitrixErrorDecoder implements ErrorDecoder {
    @Override
    public Exception decode(String methodKey, Response response) {
        String body = null;

        Exception foundException = null;
        try {
            if (response.body() != null) {
                body = new String(response.body().asInputStream().readAllBytes(), StandardCharsets.UTF_8);
            }
        } catch (IOException exception) {
            log.debug("Can't read response body for method: {}", methodKey, exception);
            foundException = exception;
        }

        final String message = String.format("Bitrix error [%s]: status %s, body: %s", method<PERSON>ey, response.status(), body);
        if (foundException != null) {
            return new BitrixClientException(message, foundException);
        }

        return new BitrixClientException(message);
    }
}
