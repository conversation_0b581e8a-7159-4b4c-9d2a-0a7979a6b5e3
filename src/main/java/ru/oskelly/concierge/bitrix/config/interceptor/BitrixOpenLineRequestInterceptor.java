package ru.oskelly.concierge.bitrix.config.interceptor;

import feign.RequestInterceptor;
import feign.RequestTemplate;
import org.springframework.beans.factory.annotation.Value;

public class BitrixOpenLineRequestInterceptor implements RequestInterceptor {

    @Value("${bitrix-open-line.webhook}")
    private String webhook;

    @Value("${bitrix-open-line.user-id}")
    private Long userId;

    @Override
    public void apply(RequestTemplate requestTemplate) {
        if (requestTemplate.url().startsWith("/rest/")) {
            return;
        }
        String action = requestTemplate.url().replaceAll("/", "");
        String updatedUrl = String.format("/rest/%s/%s/%s", userId, webhook, action);
        requestTemplate.uri(updatedUrl, false);
    }
}
