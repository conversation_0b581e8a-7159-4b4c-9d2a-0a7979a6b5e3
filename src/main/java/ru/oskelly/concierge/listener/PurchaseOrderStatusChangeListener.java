package ru.oskelly.concierge.listener;

import lombok.RequiredArgsConstructor;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import ru.oskelly.concierge.event.PurchaseOrderStatusChangedEvent;
import ru.oskelly.concierge.service.PurchaseOrderBitrixSynchronizer;

/**
 * Слушатель изменения статуса заказа на покупку.
 * При получении события инициируется экспорт заказа в Bitrix.
 */
@Component
@RequiredArgsConstructor
public class PurchaseOrderStatusChangeListener {

    private final PurchaseOrderBitrixSynchronizer bitrixSynchronizer;

    @EventListener
    public void onEvent(PurchaseOrderStatusChangedEvent event) {
        TransactionSynchronizationManager.registerSynchronization(
            new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    bitrixSynchronizer.exportOrder(event.getOrderId(), null);
                }
            }
        );
    }
}
