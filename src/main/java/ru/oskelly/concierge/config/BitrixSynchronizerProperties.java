package ru.oskelly.concierge.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import ru.oskelly.concierge.data.model.enums.PurchaseOrderStatusEnum;

import java.time.Duration;
import java.util.Set;

@Data
@Component
@ConfigurationProperties(prefix = "concierge.bitrix.synchronizer")
public class BitrixSynchronizerProperties {
    /**
     * Размер страницы для пакетной обработки заявок
     */
    private int pageSize = 50;

    /**
     * Максимальное количество попыток повтора для неудачных операций
     */
    private int maxRetryAttempts = 3;

    /**
     * Задержка между попытками повтора
     */
    private Duration retryDelay = Duration.ofSeconds(1);

    /**
     * Тайм-аут для HTTP-запросов к API Bitrix
     */
    private Duration requestTimeout = Duration.ofSeconds(30);

    /**
     * Набор статусов заявок, которые должны синхронизироваться с Bitrix
     */
    private Set<PurchaseOrderStatusEnum> synchronizableStatuses = Set.of(
        PurchaseOrderStatusEnum.NEW,
        PurchaseOrderStatusEnum.IN_PROGRESS_SALES,
        PurchaseOrderStatusEnum.AWAITING_SOURCER,
        PurchaseOrderStatusEnum.IN_PROGRESS_SOURCER,
        PurchaseOrderStatusEnum.AWAITING_SEND_TO_CLIENT,
        PurchaseOrderStatusEnum.AWAITING_CLIENT_ANSWER,
        PurchaseOrderStatusEnum.DONE
    );

    /**
     * Следует ли быстро завершать работу при первой ошибке во время пакетной обработки
     */
    private boolean failFast = false;
}