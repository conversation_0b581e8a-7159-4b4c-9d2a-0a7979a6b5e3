package ru.oskelly.concierge.schedulers;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import ru.oskelly.concierge.service.PurchaseOrderBitrixSynchronizer;

import java.util.UUID;

/**
 * Синхронизация заявок с Bitrix24.
 * Экспорт данных из Concierge в Bitrix24.
 */
@Slf4j
@Component
@RequiredArgsConstructor
@ConditionalOnExpression("${bitrix.scheduler.order-sync.enabled:false}")
public class PurchaseOrderBitrixSyncScheduler {

    private final PurchaseOrderBitrixSynchronizer synchronizer;

    @Scheduled(cron = "${bitrix.scheduler.order-sync.cron:0 0/5 * * * ?}")
    public void run() {
        final String uuid = UUID.randomUUID().toString();
        final String threadName = Thread.currentThread().getName();
        log.info("Запуск экспорта заявок в Bitrix. UUID: {}, Thread: {}", uuid, threadName);
        synchronizer.exportOrders();
        log.info("Успешное завершение экспорта заявок в Bitrix. UUID: {}, Thread: {}", uuid, threadName);
    }
}
