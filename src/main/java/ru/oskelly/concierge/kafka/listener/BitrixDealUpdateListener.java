package ru.oskelly.concierge.kafka.listener;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;
import ru.oskelly.concierge.kafka.dto.BitrixDealUpdateMessage;
import ru.oskelly.concierge.service.PurchaseOrderBitrixSynchronizer;

/**
 * Слушатель синхронизации сделок Bitrix.
 * При получении сообщения инициируется импорт заказа из Bitrix в систему.
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class BitrixDealUpdateListener {
    private final PurchaseOrderBitrixSynchronizer bitrixSynchronizer;

    @KafkaListener(
        topics = "${kafka.topics.bitrix-deal-update-requests-topic.name}",
        autoStartup = "${kafka.topics.bitrix-deal-update-requests-topic.enabled}"
    )
    public void handleEvent(@Payload BitrixDealUpdateMessage message, Acknowledgment ack) {
        log.info("Получено обновление сделки Bitrix: {}", message);
        bitrixSynchronizer.importOrder(message.dealId());
        ack.acknowledge();
    }
}
