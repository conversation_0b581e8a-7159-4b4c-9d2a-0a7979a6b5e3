package ru.oskelly.concierge.kafka.listener;

import lombok.RequiredArgsConstructor;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Component;
import ru.oskelly.concierge.kafka.dto.PurchaserUserUpdateMessage;
import ru.oskelly.concierge.service.PurchaseOrderService;

@Component
@RequiredArgsConstructor
public class PurchaserPhoneVerificationListener {

    private final PurchaseOrderService purchaseOrderService;

    @KafkaListener(
        topics = "${kafka.topics.phone-verification-topic.name}",
        autoStartup = "${kafka.topics.phone-verification-topic.enabled}"
    )
    public void handleEvent(PurchaserUserUpdateMessage event, Acknowledgment ack) {
        purchaseOrderService.updateCustomerId(event.oldUserId(), event.newUserId());
        ack.acknowledge();
    }
}
