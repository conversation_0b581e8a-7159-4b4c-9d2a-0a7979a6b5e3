package ru.oskelly.concierge.kafka.listener;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;
import ru.oskelly.concierge.controller.dto.managerdto.SourcerInfoDTO;
import ru.oskelly.concierge.kafka.dto.AvailableSourcerMessage;
import ru.oskelly.concierge.service.OrderAssignmentSourcerService;

@Component
@RequiredArgsConstructor
@Slf4j
public class AvailableSourcerListener {

    private final OrderAssignmentSourcerService orderAssignmentSourcerService;

    @KafkaListener(
        topics = "${kafka.topics.sourcer-available-topic.name}",
        autoStartup = "${kafka.topics.sourcer-available-topic.enabled}"
    )
    public void handleEvent(@Payload AvailableSourcerMessage message, Acknowledgment ack) {
        log.info("Получение сообщение для назначения сорсера: {}", message);
        final SourcerInfoDTO sourcer = SourcerInfoDTO.builder()
            .sourcerId(message.sourcerId())
            .email(message.email())
            .build();

        orderAssignmentSourcerService.assignToSourcer(message.orderId(), sourcer);

        ack.acknowledge();
    }
}
