package ru.oskelly.concierge.kafka.listener;

import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;
import ru.oskelly.concierge.controller.dto.AvailableSalesDTO;
import ru.oskelly.concierge.kafka.dto.AvailableSalesMessage;
import ru.oskelly.concierge.service.OrderAssignmentSalesService;

@Component
@RequiredArgsConstructor
@Slf4j
public class AvailableSalesListener {

    private final OrderAssignmentSalesService orderAssignmentSalesService;

    @KafkaListener(
        topics = "${kafka.topics.sales-available-topic.name}",
        autoStartup = "${kafka.topics.sales-available-topic.enabled}"
    )
    public void handleEvent(@Payload @Valid AvailableSalesMessage message, Acknowledgment ack) {
        log.info("Получение сообщение для назначения сейлза: {}", message);
        AvailableSalesDTO sales = new AvailableSalesDTO(
            message.salesId(),
            message.fio(),
            message.role(),
            message.email());

        orderAssignmentSalesService.assignToSales(message.orderId(), sales);

        ack.acknowledge();
    }
}
