package ru.oskelly.concierge.kafka.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import ru.oskelly.concierge.data.model.enums.Roles;

/**
 * Сообщение, содержащее свободного Сейлза для заявки
 *
 * @param orderId идентификатор заявки
 * @param salesId идентификатор Сейлза
 * @param fio     ФИО
 * @param role    роль
 */
public record AvailableSalesMessage(
    @NotNull(message = "Идентификатор заявки обязателен для заполнения")
    Long orderId,

    @NotNull(message = "Идентификатор Сейлза обязателен для заполнения")
    Long salesId,

    String fio,

    @NotNull(message = "Роль обязательна для заполнения")
    Roles role,

    @NotBlank(message = "Email Сейлза обязателен для заполнения")
    String email
) {
}
