package ru.oskelly.concierge.kafka.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * Сообщение, содержащее свободного Сорсера для заявки
 *
 * @param orderId   идентификатор заявки
 * @param sourcerId идентификатор Сорсера
 */
public record AvailableSourcerMessage(
    @NotNull(message = "Идентификатор заявки обязателен для заполнения")
    Long orderId,

    @NotNull(message = "Идентификатор Сорсера обязателен для заполнения")
    Long sourcerId,

    @NotBlank(message = "Email Сорсера обязателен для заполнения")
    String email
) {
}
