package ru.oskelly.concierge.data.mapper;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import ru.oskelly.concierge.controller.dto.BuyerOffers;
import ru.oskelly.concierge.controller.dto.ImageDTO;
import ru.oskelly.concierge.controller.dto.OfferDTO;
import ru.oskelly.concierge.controller.dto.ProductConditionDTO;
import ru.oskelly.concierge.controller.dto.ProductPlatformDTO;
import ru.oskelly.concierge.controller.dto.ProposedOfferDTO;
import ru.oskelly.concierge.controller.dto.SellerInfoDTO;
import ru.oskelly.concierge.controller.dto.ShimpentSizeDTO;
import ru.oskelly.concierge.controller.mercaux.dto.ShopperInfoDTO;
import ru.oskelly.concierge.data.model.Offer;
import ru.oskelly.concierge.data.model.PersonalShopper;
import ru.oskelly.concierge.data.model.ProposedOffer;
import ru.oskelly.concierge.data.model.PurchaseOrder;
import ru.oskelly.concierge.data.model.Shipment;
import ru.oskelly.concierge.data.model.enums.AccessSource;
import ru.oskelly.concierge.data.model.enums.Currency;
import ru.oskelly.concierge.data.model.enums.InteractionType;
import ru.oskelly.concierge.data.model.enums.OfferType;
import ru.oskelly.concierge.data.model.enums.PaymentFormat;
import ru.oskelly.concierge.data.model.enums.PurchaseOrderSourceEnum;
import ru.oskelly.concierge.data.model.enums.PurchaseOrderStatusEnum;
import ru.oskelly.concierge.data.model.enums.Roles;
import ru.oskelly.concierge.data.repository.PersonalShopperRepository;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertSame;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ContextConfiguration(classes = {OfferMapperImpl.class})
@ExtendWith(SpringExtension.class)
class OfferMapperTest {

    private OfferMapper offerMapper;

    @Mock
    private PersonalShopperRepository personalShopperRepository;

    @BeforeEach
    void setUp() {
        offerMapper = new OfferMapperImpl();
    }

    private PurchaseOrder createPurchaseOrder() {
        PurchaseOrder order = new PurchaseOrder();
        order.setBitrixDealId(1L);
        order.setChangeDate(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC));
        order.setComments(new HashSet<>());
        order.setCreationDate(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC));
        order.setCustomerId(1L);
        order.setCustomerNickName("Customer Nick Name");
        order.setCustomerPhone("6625550144");
        order.setDescription("The characteristics of someone or something");
        order.setId(1L);
        order.setImages(new ArrayList<ImageDTO>());
        order.setLink("Link");
        order.setOrders(new ArrayList<Long>());
        order.setReasonReturn("Just cause");
        order.setRejectionDescription("Rejection Description");
        order.setRejectionReason("Just cause");
        order.setSalesFio("Sales Fio");
        order.setSalesId(1L);
        order.setSalesNickName("Sales Nick Name");
        order.setSalesRole(Roles.SALES);
        order.setShipments(new HashSet<>());
        order.setSource(PurchaseOrderSourceEnum.APP);
        order.setSourcerId(1L);
        order.setStatus(PurchaseOrderStatusEnum.CREATED);
        return order;
    }

    private Shipment createShipment(PurchaseOrder purchaseOrder) {
        Shipment shipment = new Shipment();
        shipment.setBrandId(1L);
        shipment.setBrandName("Brand Name");
        shipment.setBrandTransliterateName("Brand Transliterate Name");
        shipment.setCategoryId(1L);
        shipment.setCategoryName("Category Name");
        shipment.setColorAttributeId(1L);
        shipment.setColorAttributeName("Color Attribute Name");
        shipment.setComment("Comment");
        shipment.setCreatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC));
        shipment.setCreatorId(1L);
        shipment.setDescription("The characteristics of someone or something");
        shipment.setId(1L);
        shipment.setImages(new ArrayList<ImageDTO>());
        shipment.setLinks(new ArrayList<>());
        shipment.setMaterialAttributeId(1L);
        shipment.setMaterialAttributeName("Material Attribute Name");
        shipment.setModelId(1L);
        shipment.setModelName("Model Name");
        shipment.setOffers(new HashSet<>());
        shipment.setPurchaseOrder(purchaseOrder);
        shipment.setShipmentSize(new ShimpentSizeDTO("Type", 1L, new HashSet<String>()));
        return shipment;
    }

    private Offer createOffer(Shipment shipment) {
        Offer offer = new Offer();
        offer.setCreationDate(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC));
        offer.setId(1L);
        offer.setIsSentToCustomer(true);
        offer.setProductId(1L);
        offer.setProposedOffers(new HashSet<>());
        offer.setShipment(shipment);
        offer.setType(OfferType.PLATFORM_PRODUCT);
        offer.setUserId(1L);
        offer.setSellerId(1L);
        offer.setSellerType("BUYER");
        return offer;
    }

    private PersonalShopper createPersonalShopper() {
        PersonalShopper shopper = new PersonalShopper();
        shopper.setAccessSource(AccessSource.PERSONAL_BOUTIQUE_ACCESS);
        shopper.setBrands(new HashSet<>());
        shopper.setCategories(new HashSet<>());
        shopper.setDateShopperStatus(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC));
        shopper.setEmail("<EMAIL>");
        shopper.setId(1L);
        shopper.setName("Name");
        shopper.setNickname("Nickname");
        shopper.setPaymentFormat(PaymentFormat.PREPAYMENT);
        shopper.setPriority(true);
        shopper.setUserId(1L);
        return shopper;
    }

    private ProposedOfferDTO createProposedOfferDTO(Long id) {
        return new ProposedOfferDTO(
                id, 1L, new BigDecimal("2.3"),
                LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC),
                LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC),
                LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC),
                Currency.USD, new BigDecimal("2.3"), new BigDecimal("2.3"),
                Boolean.TRUE, Boolean.TRUE, Boolean.TRUE, new BigDecimal("2.3"), "Comment",
                new ProductConditionDTO(1L, "Name", "The characteristics of someone or something"), 1L
        );
    }

    private ProposedOffer createProposedOffer(Long id) {
        ProposedOffer offer = new ProposedOffer();
        offer.setId(id);
        offer.setComment("Comment");
        offer.setCommission(new BigDecimal("2.3"));
        offer.setCompleteSet(true);
        offer.setCreationDate(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC));
        offer.setCurrency(Currency.USD);
        offer.setCurrencyPrice(new BigDecimal("2.3"));
        offer.setCurrencyRate(new BigDecimal("2.3"));
        offer.setDeliveryDate(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC));
        offer.setHasCustomCommission(true);
        offer.setHasReceipt(true);
        offer.setIsSentToCustomer(true);
        offer.setOffer(createOffer(createShipment(createPurchaseOrder())));
        offer.setProductConditionId(1L);
        offer.setProposedProductId(1L);
        offer.setRublePrice(new BigDecimal("2.3"));
        offer.setValidUntil(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC));
        return offer;
    }

    @Nested
    @DisplayName("Тесты mapShopperInfo")
    class MapShopperInfoTests {

        @Test
        @DisplayName("Когда InteractionType равен null")
        void testMapShopperInfoInteractionTypeNull() {
            PersonalShopper shopper = createPersonalShopper();
            shopper.setInteractionType(null);
            when(personalShopperRepository.findByUserId(1L)).thenReturn(Optional.of(shopper));

            Offer offer = createOffer(createShipment(createPurchaseOrder()));
            ShopperInfoDTO result = offerMapper.mapShopperInfo(offer, personalShopperRepository);

            assertEquals("Name", result.fio());
            assertEquals("Nickname", result.nickName());
            assertEquals("PREPAYMENT", result.paymentFormat().code());
            assertEquals("Предоплата", result.paymentFormat().localizedDescription());
            verify(personalShopperRepository).findByUserId(1L);
        }

        @Test
        @DisplayName("Когда PaymentFormat равен null")
        void testMapShopperInfoPaymentFormatNull() {
            PersonalShopper shopper = createPersonalShopper();
            shopper.setPaymentFormat(null);
            shopper.setInteractionType(Set.of(InteractionType.INTERNET_SITES));
            when(personalShopperRepository.findByUserId(1L)).thenReturn(Optional.of(shopper));

            Offer offer = createOffer(createShipment(createPurchaseOrder()));
            ShopperInfoDTO result = offerMapper.mapShopperInfo(offer, personalShopperRepository);

            assertEquals("Name", result.fio());
            assertEquals("Nickname", result.nickName());
            assertEquals(1, result.interactionTypes().size());
            var interactionTypesList = new ArrayList<>(result.interactionTypes());
            assertEquals("INTERNET_SITES", interactionTypesList.get(0).code());
            assertEquals("Интернет сайты", interactionTypesList.get(0).localizedDescription());
            verify(personalShopperRepository).findByUserId(1L);
        }

        @Test
        @DisplayName("Когда SellerId равен null")
        void testMapShopperInfoSellerIdNull() {
            Offer offer = mock(Offer.class);
            when(offer.getSellerId()).thenReturn(null);
            setupMockOffer(offer);

            ShopperInfoDTO result = offerMapper.mapShopperInfo(offer, personalShopperRepository);

            assertNull(result.id());
            assertNull(result.fio());
            assertNull(result.nickName());
            assertNull(result.paymentFormat());
            assertTrue(result.interactionTypes().isEmpty());
            verify(offer).getSellerId();
        }

        @Test
        @DisplayName("Когда SellerType равен BUYER")
        void testMapShopperInfoSellerTypeBuyer() {
            PersonalShopper shopper = createPersonalShopper();
            shopper.setInteractionType(Set.of(InteractionType.INTERNET_SITES));
            when(personalShopperRepository.findByUserId(1L)).thenReturn(Optional.of(shopper));

            Offer offer = mock(Offer.class);
            when(offer.getSellerId()).thenReturn(1L);
            when(offer.getSellerType()).thenReturn("BUYER");
            setupMockOffer(offer);

            ShopperInfoDTO result = offerMapper.mapShopperInfo(offer, personalShopperRepository);

            assertEquals("Name", result.fio());
            assertEquals("Nickname", result.nickName());
            assertEquals("PREPAYMENT", result.paymentFormat().code());
            assertEquals("Предоплата", result.paymentFormat().localizedDescription());
            assertEquals(1, result.interactionTypes().size());
            var interactionTypesList2 = new ArrayList<>(result.interactionTypes());
            assertEquals("INTERNET_SITES", interactionTypesList2.get(0).code());
            verify(personalShopperRepository).findByUserId(1L);
        }
    }

    @Nested
    @DisplayName("Тесты createShipmentFromId")
    class CreateShipmentFromIdTests {

        @Test
        @DisplayName("Когда входной параметр равен null")
        void testCreateShipmentFromIdNull() {
            assertNull(offerMapper.createShipmentFromId(null));
        }

        @Test
        @DisplayName("Когда входной параметр является валидным ID")
        void testCreateShipmentFromIdValidId() {
            Shipment result = offerMapper.createShipmentFromId(1L);

            assertEquals(1L, result.getId().longValue());
            assertNull(result.getBrandId());
            assertTrue(result.getImages().isEmpty());
            assertTrue(result.getLinks().isEmpty());
            assertTrue(result.getOffers().isEmpty());
        }
    }

    @Nested
    @DisplayName("Тесты createOfferFromId")
    class CreateOfferFromIdTests {

        @Test
        @DisplayName("Когда входной параметр равен null")
        void testCreateOfferFromIdNull() {
            assertNull(offerMapper.createOfferFromId(null));
        }

        @Test
        @DisplayName("Когда входной параметр является валидным ID")
        void testCreateOfferFromIdValidId() {
            Offer result = offerMapper.createOfferFromId(1L);

            assertEquals(1L, result.getId().longValue());
            assertNull(result.getProductId());
            assertFalse(result.getIsSentToCustomer());
            assertTrue(result.getProposedOffers().isEmpty());
        }
    }

    @Nested
    @DisplayName("Тесты updateProposedOffers")
    class UpdateProposedOffersTests {

        @Test
        @DisplayName("Когда предоставлены новые предложения")
        void testUpdateProposedOffersWithNewOffers() {
            LinkedHashSet<ProposedOfferDTO> newOffers = new LinkedHashSet<>();
            newOffers.add(createProposedOfferDTO(null));

            assertEquals(1, offerMapper.updateProposedOffers(newOffers, null).size());
        }

        @Test
        @DisplayName("Когда ID существующего предложения равен null")
        void testUpdateProposedOffersExistingIdNull() {
            LinkedHashSet<ProposedOffer> existingOffers = new LinkedHashSet<>();
            existingOffers.add(createProposedOffer(null));

            assertSame(existingOffers, offerMapper.updateProposedOffers(null, existingOffers));
        }

        @Test
        @DisplayName("Когда новые предложения пусты и ID существующего предложения равен null")
        void testUpdateProposedOffersEmptyNewOffersIdNull() {
            LinkedHashSet<ProposedOffer> existingOffers = new LinkedHashSet<>();
            existingOffers.add(createProposedOffer(null));

            assertTrue(offerMapper.updateProposedOffers(new HashSet<>(), existingOffers).isEmpty());
        }

        @Test
        @DisplayName("Когда новые предложения пусты и ID существующего предложения валиден")
        void testUpdateProposedOffersEmptyNewOffersValidId() {
            LinkedHashSet<ProposedOffer> existingOffers = new LinkedHashSet<>();
            existingOffers.add(createProposedOffer(1L));

            assertTrue(offerMapper.updateProposedOffers(new HashSet<>(), existingOffers).isEmpty());
        }

        @Test
        @DisplayName("Когда оба набора пусты")
        void testUpdateProposedOffersBothEmpty() {
            assertTrue(offerMapper.updateProposedOffers(new HashSet<>(), new HashSet<>()).isEmpty());
        }

        @Test
        @DisplayName("Когда оба входных параметра равны null")
        void testUpdateProposedOffersBothNull() {
            assertNull(offerMapper.updateProposedOffers(null, null));
        }
    }

    @Nested
    @DisplayName("Тесты mapProduct")
    class MapProductTests {

        @Test
        @DisplayName("Когда ProductId равен null")
        void testMapProductProductIdNull() {
            Offer offer = createOffer(createShipment(createPurchaseOrder()));
            offer.setProductId(null);

            assertNull(offerMapper.mapProduct(offer));
        }

        @Test
        @DisplayName("Когда ProductId валиден")
        void testMapProductValidProductId() {
            Offer offer = createOffer(createShipment(createPurchaseOrder()));

            ProductPlatformDTO result = offerMapper.mapProduct(offer);

            assertEquals(1L, result.productId().longValue());
            assertNull(result.isLiked());
            assertNull(result.conditionId());
            assertNull(result.brand());
        }
    }

    @Nested
    @DisplayName("Тесты toDTO")
    class ToDTOTests {

        @Test
        @DisplayName("Когда Offer валиден")
        void testToDTOValidOffer() {
            Offer offer = createOffer(createShipment(createPurchaseOrder()));
            offer.setSellerId(1L);
            offer.setSellerType("BUYER");

            PersonalShopper personalShopper = createPersonalShopper();
            when(personalShopperRepository.findByUserId(1L)).thenReturn(Optional.of(personalShopper));

            var result = offerMapper.toDTO(offer, personalShopperRepository);

            assertNotNull(result);
            assertEquals(offer.getId(), result.id());
            assertEquals(offer.getShipment().getId(), result.shipmentId());
            assertEquals(offer.getSellerId(), result.seller().sellerId());
            assertNotNull(result.shopper());
            assertNotNull(result.product());
        }

        @Test
        @DisplayName("Когда Offer равен null")
        void testToDTONullOffer() {
            var result = offerMapper.toDTO(null, personalShopperRepository);
            assertNull(result);
        }
    }

    @Nested
    @DisplayName("Тесты toEntity")
    class ToEntityTests {

        @Test
        @DisplayName("Когда OfferDTO валиден")
        void testToEntityValidDTO() {
            // Создаем тестовый DTO
            var offerDTO = new OfferDTO(
                    1L, // id
                    null, // comparisonCriteria
                    2L, // shipmentId
                    new SellerInfoDTO(3L, null, null, null, null, null), // seller
                    null, // shopper
                    new ProductPlatformDTO(
                            4L, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null
                    ), // product
                    OfferType.PLATFORM_PRODUCT, // type
                    "BUYER", // sellerType
                    LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC), // creationDate
                    new BuyerOffers(new HashSet<>()) // buyerOffers
            );

            var result = offerMapper.toEntity(offerDTO);

            assertNotNull(result);
            assertEquals(offerDTO.id(), result.getId());
            assertEquals(offerDTO.seller().sellerId(), result.getSellerId());
            assertEquals(offerDTO.product().productId(), result.getProductId());
            assertEquals(offerDTO.type(), result.getType());
            assertEquals(offerDTO.sellerType(), result.getSellerType());
            assertNotNull(result.getShipment());
            assertEquals(offerDTO.shipmentId(), result.getShipment().getId());
        }

        @Test
        @DisplayName("Когда OfferDTO равен null")
        void testToEntityNullDTO() {
            var result = offerMapper.toEntity(null);
            assertNull(result);
        }
    }

    @Nested
    @DisplayName("Тесты toProposedOfferDTO")
    class ToProposedOfferDTOTests {

        @Test
        @DisplayName("Когда ProposedOffer валиден")
        void testToProposedOfferDTOValid() {
            ProposedOffer proposedOffer = OfferMapperTest.this.createProposedOffer(1L);

            var result = offerMapper.toProposedOfferDTO(proposedOffer);

            assertNotNull(result);
            assertEquals(proposedOffer.getId(), result.id());
            assertEquals(proposedOffer.getRublePrice(), result.rublePrice());
            assertEquals(proposedOffer.getDeliveryDate(), result.deliveryDate());
            assertEquals(proposedOffer.getValidUntil(), result.validUntil());
            assertEquals(proposedOffer.getCurrency(), result.currency());
            assertEquals(proposedOffer.getCurrencyPrice(), result.currencyPrice());
            assertEquals(proposedOffer.isHasReceipt(), result.hasReceipt());
            assertEquals(proposedOffer.isCompleteSet(), result.isCompleteSet());
            assertEquals(proposedOffer.getComment(), result.comment());
        }

        @Test
        @DisplayName("Когда ProposedOffer равен null")
        void testToProposedOfferDTONull() {
            var result = offerMapper.toProposedOfferDTO(null);
            assertNull(result);
        }
    }

    @Nested
    @DisplayName("Тесты toProposedOfferEntity")
    class ToProposedOfferEntityTests {

        @Test
        @DisplayName("Когда ProposedOfferDTO валиден")
        void testToProposedOfferEntityValid() {
            var dto = createProposedOfferDTO(1L);

            var result = offerMapper.toProposedOfferEntity(dto);

            assertNotNull(result);
            assertEquals(dto.id(), result.getId());
            assertEquals(dto.rublePrice(), result.getRublePrice());
            assertEquals(dto.deliveryDate(), result.getDeliveryDate());
            assertEquals(dto.validUntil(), result.getValidUntil());
            assertEquals(dto.currency(), result.getCurrency());
            assertEquals(dto.currencyPrice(), result.getCurrencyPrice());
            assertEquals(dto.hasReceipt(), result.isHasReceipt());
            assertEquals(dto.isCompleteSet(), result.isCompleteSet());
            assertEquals(dto.comment(), result.getComment());
        }

        @Test
        @DisplayName("Когда ProposedOfferDTO равен null")
        void testToProposedOfferEntityNull() {
            var result = offerMapper.toProposedOfferEntity(null);
            assertNull(result);
        }
    }

    @Nested
    @DisplayName("Тесты updateProposedOfferEntity")
    class UpdateProposedOfferEntityTests {

        @Test
        @DisplayName("Когда обновляем существующий ProposedOffer")
        void testUpdateProposedOfferEntityValid() {
            ProposedOffer target = createProposedOffer(1L);
            // Создаем DTO с другой ценой
            var dto = new ProposedOfferDTO(
                    2L, 1L, new BigDecimal("5.7"), // другая цена
                    LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC),
                    LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC),
                    LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC),
                    Currency.USD, new BigDecimal("2.3"), new BigDecimal("2.3"),
                    Boolean.TRUE, Boolean.TRUE, Boolean.TRUE, new BigDecimal("2.3"), "Updated Comment",
                    new ProductConditionDTO(1L, "Name", "The characteristics of someone or something"), 1L
            );

            BigDecimal originalRublePrice = target.getRublePrice();

            offerMapper.updateProposedOfferEntity(dto, target);

            // ID не должен измениться
            assertEquals(1L, target.getId());
            // Другие поля должны обновиться
            assertEquals(dto.rublePrice(), target.getRublePrice());
            assertNotEquals(originalRublePrice, target.getRublePrice());
        }

        @Test
        @DisplayName("Когда DTO равен null")
        void testUpdateProposedOfferEntityNullDTO() {
            ProposedOffer target = createProposedOffer(1L);
            BigDecimal originalRublePrice = target.getRublePrice();

            offerMapper.updateProposedOfferEntity(null, target);

            // Значения не должны измениться
            assertEquals(originalRublePrice, target.getRublePrice());
        }
    }

    @Nested
    @DisplayName("Тесты updateToEntity")
    class UpdateToEntityTests {

        @Test
        @DisplayName("Когда обновляем существующий Offer")
        void testUpdateToEntityValid() {
            Offer target = createOffer(createShipment(createPurchaseOrder()));
            var offerDTO = new OfferDTO(
                    2L, // id
                    null, // comparisonCriteria
                    3L, // shipmentId
                    new SellerInfoDTO(4L, null, null, null, null, null), // seller
                    null, // shopper
                    new ProductPlatformDTO(
                            5L, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null
                    ), // product
                    OfferType.BUYER_OFFER, // type
                    "SELLER", // sellerType
                    LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC), // creationDate
                    new BuyerOffers(new HashSet<>()) // buyerOffers
            );

            Long originalId = target.getId();

            offerMapper.updateToEntity(offerDTO, target);

            // ID должен обновиться согласно DTO (он не игнорируется в маппинге)
            assertEquals(offerDTO.id(), target.getId());
            assertNotEquals(originalId, target.getId());
            // Другие поля должны обновиться
            assertEquals(offerDTO.seller().sellerId(), target.getSellerId());
            assertEquals(offerDTO.product().productId(), target.getProductId());
            assertEquals(offerDTO.type(), target.getType());
            assertEquals(offerDTO.sellerType(), target.getSellerType());
        }

        @Test
        @DisplayName("Когда DTO равен null")
        void testUpdateToEntityNullDTO() {
            Offer target = createOffer(createShipment(createPurchaseOrder()));
            Long originalSellerId = target.getSellerId();

            offerMapper.updateToEntity(null, target);

            // Значения не должны измениться
            assertEquals(originalSellerId, target.getSellerId());
        }
    }

    @Nested
    @DisplayName("Тесты коллекций")
    class CollectionTests {

        @Test
        @DisplayName("Тест toEntityList")
        void testToEntityList() {
            var offerDTO = new OfferDTO(
                    1L, // id
                    null, // comparisonCriteria
                    2L, // shipmentId
                    new SellerInfoDTO(3L, null, null, null, null, null), // seller
                    null, // shopper
                    new ProductPlatformDTO(
                            4L, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null
                    ), // product
                    OfferType.PLATFORM_PRODUCT, // type
                    "BUYER", // sellerType
                    LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC), // creationDate
                    new BuyerOffers(new HashSet<>()) // buyerOffers
            );

            List<ru.oskelly.concierge.controller.dto.OfferDTO> dtoList = Arrays.asList(offerDTO);

            var result = offerMapper.toEntityList(dtoList);

            assertNotNull(result);
            assertEquals(1, result.size());
            assertEquals(offerDTO.id(), result.get(0).getId());
        }

        @Test
        @DisplayName("Тест toDTOList")
        void testToDTOList() {
            Offer offer = createOffer(createShipment(createPurchaseOrder()));
            List<Offer> offerList = Arrays.asList(offer);

            when(personalShopperRepository.findByUserId(any())).thenReturn(Optional.empty());

            var result = offerMapper.toDTOList(offerList, personalShopperRepository);

            assertNotNull(result);
            assertEquals(1, result.size());
            assertEquals(offer.getId(), result.get(0).id());
        }

        @Test
        @DisplayName("Тест toProposedOfferDTOSet")
        void testToProposedOfferDTOSet() {
            ProposedOffer proposedOffer = createProposedOffer(1L);
            Set<ProposedOffer> offerSet = Set.of(proposedOffer);

            var result = offerMapper.toProposedOfferDTOSet(offerSet);

            assertNotNull(result);
            assertEquals(1, result.size());
            assertEquals(proposedOffer.getId(), result.iterator().next().id());
        }

        @Test
        @DisplayName("Тест toProposedOfferEntitySet")
        void testToProposedOfferEntitySet() {
            var dto = createProposedOfferDTO(1L);
            Set<ProposedOfferDTO> dtoSet = Set.of(dto);

            var result = offerMapper.toProposedOfferEntitySet(dtoSet);

            assertNotNull(result);
            assertEquals(1, result.size());
            assertEquals(dto.id(), result.iterator().next().getId());
        }

        @Test
        @DisplayName("Тест с null коллекциями")
        void testNullCollections() {
            assertNull(offerMapper.toEntityList(null));
            assertNull(offerMapper.toDTOList(null, personalShopperRepository));
            assertNull(offerMapper.toProposedOfferDTOSet(null));
            assertNull(offerMapper.toProposedOfferEntitySet(null));
        }
    }

    private void setupMockOffer(Offer offer) {
        doNothing().when(offer).setCreationDate(any());
        doNothing().when(offer).setId(any());
        doNothing().when(offer).setIsSentToCustomer(any());
        doNothing().when(offer).setProductId(any());
        doNothing().when(offer).setProposedOffers(any());
        doNothing().when(offer).setSellerId(any());
        doNothing().when(offer).setSellerType(any());
        doNothing().when(offer).setShipment(any());
        doNothing().when(offer).setType(any());
        doNothing().when(offer).setUserId(any());

        offer.setCreationDate(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC));
        offer.setId(1L);
        offer.setIsSentToCustomer(true);
        offer.setProductId(1L);
        offer.setProposedOffers(new HashSet<>());
        offer.setSellerId(1L);
        offer.setSellerType("Seller Type");
        offer.setShipment(createShipment(createPurchaseOrder()));
        offer.setType(OfferType.PLATFORM_PRODUCT);
        offer.setUserId(1L);
    }
}