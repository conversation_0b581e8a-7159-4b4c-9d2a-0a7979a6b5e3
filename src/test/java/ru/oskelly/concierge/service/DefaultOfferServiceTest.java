package ru.oskelly.concierge.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;
import ru.oskelly.concierge.common.ContextConstants;
import ru.oskelly.concierge.common.ThreadLocalContext;
import ru.oskelly.concierge.controller.dto.BuyerOffers;
import ru.oskelly.concierge.controller.dto.DescriptionStructureEnum;
import ru.oskelly.concierge.controller.dto.OfferDTO;
import ru.oskelly.concierge.controller.dto.ProductConditionDTO;
import ru.oskelly.concierge.controller.dto.ProductPlatformDTO;
import ru.oskelly.concierge.controller.dto.ProposedOfferDTO;
import ru.oskelly.concierge.controller.dto.SellerInfoDTO;
import ru.oskelly.concierge.controller.dto.ShipmentOffersDTO;
import ru.oskelly.concierge.data.mapper.OfferMapper;
import ru.oskelly.concierge.data.mapper.ProposedOfferMapper;
import ru.oskelly.concierge.data.model.Offer;
import ru.oskelly.concierge.data.model.PurchaseOrder;
import ru.oskelly.concierge.data.model.Shipment;
import ru.oskelly.concierge.data.model.enums.Currency;
import ru.oskelly.concierge.data.model.enums.OfferType;
import ru.oskelly.concierge.data.model.enums.PurchaseOrderStatusEnum;
import ru.oskelly.concierge.data.repository.OfferRepository;
import ru.oskelly.concierge.data.repository.ShipmentRepository;
import ru.oskelly.concierge.exception.OfferCountException;
import ru.oskelly.concierge.service.dto.ComparisonCriterion;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class DefaultOfferServiceTest {

    @InjectMocks
    private DefaultOfferService offerService;

    @Mock
    private OfferRepository offerRepository;

    @Mock
    private ShipmentRepository shipmentRepository;

    @Mock
    private OfferMapper offerMapper;

    @Mock
    private ProposedOfferMapper proposedOfferMapper;

    @BeforeEach
    void setUp() {
        ThreadLocalContext.put(ContextConstants.USER_ID, 123L);
        ReflectionTestUtils.setField(offerService, "maxCountProductPlatform", 5);
        ReflectionTestUtils.setField(offerService, "maxCountOffer", 2);
    }

    @Test
    void addingProducts_maxCountExceeded_throwsException() {
        Long shipmentId = 1L;
        ShipmentOffersDTO dto = createValidShipmentOffersDTO(OfferType.PLATFORM_PRODUCT);
        
        when(offerRepository.findByShipment_IdAndProductIdNotNull(shipmentId))
                .thenReturn(mockOffers(5, OfferType.PLATFORM_PRODUCT));

        assertThrows(OfferCountException.class, () -> offerService.addingProducts(dto));
    }

    @Test
    void deleteProduct_success() {
        doNothing().when(offerRepository).deleteById(1L);
        when(offerRepository.findById(1L)).thenReturn(Optional.of(mockOffer()));

        assertNull(offerService.deleteProduct(1L));
    }

    private ShipmentOffersDTO createValidShipmentOffersDTO(OfferType type) {
        SellerInfoDTO seller = new SellerInfoDTO(
                100L,
                new DescriptionStructureEnum("INDIVIDUAL", "Individual Seller"),
                "John Doe",
                "<EMAIL>",
                "john.doe",
                "https://example.com/avatar.jpg"
        );

        ProposedOfferDTO proposedOffer = new ProposedOfferDTO(
                1L,
                1001L,
                BigDecimal.valueOf(15000),
                ZonedDateTime.now().plusDays(10),
                ZonedDateTime.now().plusDays(2),
                ZonedDateTime.now(),
                Currency.EUR,
                BigDecimal.valueOf(150),
                BigDecimal.valueOf(85.45),
                true,
                true,
                false,
                BigDecimal.valueOf(15),
                "Товар в отличном состоянии.",
                new ProductConditionDTO(1L, null, null),
                1L
        );

        ProductPlatformDTO product = type == OfferType.PLATFORM_PRODUCT ? 
                new ProductPlatformDTO(1L, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null) : 
                null;

        OfferDTO offerDTO = new OfferDTO(
                1L,
                Set.of(ComparisonCriterion.DELIVERY_DATE),
                1L,
                seller,
                null, // shopper
                product,
                type,
                "INDIVIDUAL",
                ZonedDateTime.now(),
                new BuyerOffers(Set.of(proposedOffer))
        );

        return new ShipmentOffersDTO(1L, List.of(offerDTO));
    }

    private List<Offer> mockOffers(int countOffers, OfferType type) {
        List<Offer> offerList = new ArrayList<>();
        for (int i = 0; i < countOffers; i++) {
            offerList.add(
                    Offer.builder()
                            .id(1L + i)
                            .userId(123L)
                            .type(type)
                            .productId(23L + i)
                            .build()
            );
        }
        return offerList;
    }

    private Offer mockOffer() {
        return Offer.builder()
                .userId(123L)
                .sellerType("INDIVIDUAL")
                .type(OfferType.PLATFORM_PRODUCT)
                .productId(1L)
                .build();
    }
}